/**
 * Voice Chat Demo Component
 * Component demo để test voice chat functionality
 */

import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/shared/components/common';
import ChatInputBox from '@/shared/components/layout/chat-panel/ChatInputBox';
import { VoiceChatMode } from '@/shared/hooks/common';

/**
 * Voice Chat Demo Component
 */
const VoiceChatDemo: React.FC = () => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Array<{
    id: string;
    content: string;
    sender: 'user' | 'ai';
    timestamp: Date;
  }>>([]);
  const [voiceMode, setVoiceMode] = useState<VoiceChatMode>('both');
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: Date;
  }>>([]);

  // Ref để control voice chat từ bên ngoài
  const voiceChatRef = useRef<{
    speakText: (text: string) => Promise<void>;
    stopSpeaking: () => void;
    isPlaying: boolean;
  } | null>(null);

  // Handle menu open
  const handleOpenMenu = () => {
    console.log('[VoiceChatDemo] Menu opened');
  };

  // Handle input change
  const handleInputChange = (text: string) => {
    console.log('[VoiceChatDemo] Input changed:', text);
  };

  // Handle voice transcript
  const handleVoiceTranscript = (transcript: string, isFinal: boolean) => {
    console.log('[VoiceChatDemo] Voice transcript:', transcript, 'isFinal:', isFinal);
    
    if (isFinal && transcript.trim()) {
      // Add user message
      const userMessage = {
        id: `user-${Date.now()}`,
        content: transcript.trim(),
        sender: 'user' as const,
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, userMessage]);

      // Simulate AI response after a delay
      setTimeout(() => {
        const aiResponse = `Tôi đã nghe bạn nói: "${transcript.trim()}". Đây là phản hồi mô phỏng từ AI.`;
        
        const aiMessage = {
          id: `ai-${Date.now()}`,
          content: aiResponse,
          sender: 'ai' as const,
          timestamp: new Date(),
        };
        
        setMessages(prev => [...prev, aiMessage]);

        // Speak the AI response if voice output is enabled
        if ((voiceMode === 'output' || voiceMode === 'both') && voiceChatRef.current) {
          voiceChatRef.current.speakText(aiResponse);
        }
      }, 1000);
    }
  };

  // Handle voice response
  const handleVoiceResponse = (text: string) => {
    console.log('[VoiceChatDemo] Voice response:', text);
  };

  // Add notification
  const addNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string, duration = 5000) => {
    const notification = {
      id: `notif-${Date.now()}`,
      type,
      message,
      timestamp: new Date(),
    };
    
    setNotifications(prev => [...prev, notification]);

    // Auto remove after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, duration);
  };

  // Test voice output
  const testVoiceOutput = async () => {
    if (voiceChatRef.current) {
      await voiceChatRef.current.speakText('Đây là test voice output. Xin chào từ hệ thống voice chat!');
    }
  };

  // Stop voice output
  const stopVoiceOutput = () => {
    if (voiceChatRef.current) {
      voiceChatRef.current.stopSpeaking();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Voice Chat Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Test Google Cloud Speech-to-Text và Text-to-Speech integration
        </p>
      </div>

      {/* Voice Mode Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          Voice Chat Settings
        </h3>
        
        <div className="flex flex-wrap gap-2 mb-4">
          {(['both', 'input', 'output', 'disabled'] as VoiceChatMode[]).map((mode) => (
            <button
              key={mode}
              onClick={() => setVoiceMode(mode)}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                voiceMode === mode
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </button>
          ))}
        </div>

        <div className="flex gap-2">
          <button
            onClick={testVoiceOutput}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center gap-2"
          >
            <Icon name="volume-2" size="sm" />
            Test Voice Output
          </button>
          
          <button
            onClick={stopVoiceOutput}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center gap-2"
          >
            <Icon name="volume-x" size="sm" />
            Stop Voice
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          Messages
        </h3>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {messages.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-8">
              No messages yet. Try speaking or typing a message.
            </p>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="fixed top-4 right-4 space-y-2 z-50">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-3 rounded-md shadow-lg max-w-sm ${
                notification.type === 'error'
                  ? 'bg-red-500 text-white'
                  : notification.type === 'warning'
                    ? 'bg-yellow-500 text-white'
                    : notification.type === 'success'
                      ? 'bg-green-500 text-white'
                      : 'bg-blue-500 text-white'
              }`}
            >
              <p className="text-sm">{notification.message}</p>
            </div>
          ))}
        </div>
      )}

      {/* Chat Input */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          Voice Chat Input
        </h3>
        
        <ChatInputBox
          onOpenMenu={handleOpenMenu}
          onInputChange={handleInputChange}
          placeholder="Nhập tin nhắn hoặc nhấn microphone để nói..."
          enableVoiceChat={true}
          voiceChatMode={voiceMode}
          onVoiceTranscript={handleVoiceTranscript}
          onVoiceResponse={handleVoiceResponse}
          addNotification={addNotification}
          voiceChatRef={voiceChatRef}
        />
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Instructions
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• Make sure you have configured Google Cloud credentials in your .env file</li>
          <li>• Click the microphone button to start voice input</li>
          <li>• Speak clearly and wait for the transcript to appear</li>
          <li>• The AI will respond with voice output if enabled</li>
          <li>• Use the voice mode controls to test different configurations</li>
        </ul>
      </div>
    </div>
  );
};

export default VoiceChatDemo;
