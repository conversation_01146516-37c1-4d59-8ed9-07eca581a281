/**
 * Hook để xử lý xóa thread đơn giản (không active)
 * Chỉ gọi API xóa và update UI, không cần integration
 */

import { useState, useCallback } from 'react';
import { useMutation, useQueryClient, InfiniteData } from '@tanstack/react-query';
import { ThreadsService } from '../services/threads.service';
import { THREADS_QUERY_KEYS } from '../constants';
import { markThreadAsDeleted } from '../services/deleted-thread-tracker.service';
import { GetThreadsResponse, ThreadItem } from '@/shared/types/chat-streaming.types';

interface UseSimpleThreadDeleteProps {
  /**
   * Callback khi xóa thành công
   */
  onThreadDeleted?: (threadId: string) => void;
}

export const useSimpleThreadDelete = ({ onThreadDeleted }: UseSimpleThreadDeleteProps = {}) => {
  const queryClient = useQueryClient();
  const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

  // Mutation để delete thread đơn giản
  const deleteThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setDeletingThreadId(threadId);
      
      console.log('[useSimpleThreadDelete] Deleting non-active thread:', threadId);
      
      // Chỉ gọi API xóa
      await ThreadsService.deleteThread(threadId);
      
      return { deletedThreadId: threadId };
    },
    onSuccess: ({ deletedThreadId }) => {
      console.log('[useSimpleThreadDelete] Thread deleted successfully:', deletedThreadId);

      // Mark thread as deleted để prevent future API calls
      markThreadAsDeleted(deletedThreadId);

      // Remove thread detail từ cache
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

      // Optimistic update: Remove thread từ tất cả list queries ngay lập tức
      queryClient.setQueriesData(
        {
          queryKey: THREADS_QUERY_KEYS.ALL,
          predicate: (query) => {
            const key = query.queryKey;
            return key.includes('list') || key.includes('paginated') || key.includes('infinite');
          }
        },
        (oldData: InfiniteData<GetThreadsResponse> | GetThreadsResponse | ThreadItem[] | unknown) => {
          if (!oldData) return oldData;

          console.log('[useSimpleThreadDelete] Optimistic update - removing thread:', {
            deletedThreadId,
            oldDataType: (oldData as InfiniteData<GetThreadsResponse>).pages ? 'infinite' : Array.isArray(oldData) ? 'array' : typeof oldData,
            oldDataStructure: (oldData as InfiniteData<GetThreadsResponse>).pages ? 'pages' : 'direct'
          });

          // Handle infinite query data structure
          if ((oldData as InfiniteData<GetThreadsResponse>).pages) {
            const infiniteData = oldData as InfiniteData<GetThreadsResponse>;
            const updatedData: InfiniteData<GetThreadsResponse> = {
              ...infiniteData,
              pages: infiniteData.pages.map((page: GetThreadsResponse) => ({
                ...page,
                items: page.items ? page.items.filter((thread: ThreadItem) => thread.threadId !== deletedThreadId) : [],
                meta: page.meta ? {
                  ...page.meta,
                  totalItems: Math.max(0, (page.meta.totalItems || 0) - 1)
                } : page.meta
              }))
            };

            console.log('[useSimpleThreadDelete] Updated infinite query data:', {
              originalPagesCount: infiniteData.pages.length,
              updatedPagesCount: updatedData.pages.length,
              originalItemsCount: infiniteData.pages.reduce((sum: number, page: GetThreadsResponse) => sum + (page.items?.length || 0), 0),
              updatedItemsCount: updatedData.pages.reduce((sum: number, page: GetThreadsResponse) => sum + (page.items?.length || 0), 0)
            });

            return updatedData;
          }

          // Handle regular query data structure
          if (Array.isArray(oldData)) {
            const threadsArray = oldData as ThreadItem[];
            const filtered = threadsArray.filter((thread: ThreadItem) => thread.threadId !== deletedThreadId);
            console.log('[useSimpleThreadDelete] Updated array data:', {
              originalCount: threadsArray.length,
              filteredCount: filtered.length
            });
            return filtered;
          }

          return oldData;
        }
      );

      // Force invalidate và refetch tất cả list queries
      console.log('[useSimpleThreadDelete] Invalidating queries after delete...');

      // Invalidate infinite queries
      queryClient.invalidateQueries({
        queryKey: ['threads', 'infinite'],
        refetchType: 'active' // Force refetch active queries
      });

      // Invalidate list queries
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query) => {
          const key = query.queryKey;
          const isListQuery = key.includes('list') || key.includes('paginated') || key.includes('infinite');
          const isDetailQuery = key.includes('detail');
          return isListQuery && !isDetailQuery;
        },
        refetchType: 'active'
      });

      console.log('[useSimpleThreadDelete] Queries invalidated successfully');

      // Clear loading state
      setDeletingThreadId(null);

      // Trigger callback
      onThreadDeleted?.(deletedThreadId);
    },
    onError: (error) => {
      console.error('[useSimpleThreadDelete] Failed to delete thread:', error);
      setDeletingThreadId(null);
    },
  });

  // Function để delete thread
  const deleteThread = useCallback((threadId: string) => {
    deleteThreadMutation.mutate(threadId);
  }, [deleteThreadMutation]);

  // Function để check xem thread có đang được xóa không
  const isThreadDeleting = useCallback((threadId: string) => {
    return deletingThreadId === threadId;
  }, [deletingThreadId]);

  return {
    // Actions
    deleteThread,
    
    // State
    isThreadDeleting,
    isDeleting: deleteThreadMutation.isPending,
    error: deleteThreadMutation.error,
  };
};
