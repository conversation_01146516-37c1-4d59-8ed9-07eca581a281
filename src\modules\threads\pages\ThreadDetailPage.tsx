/**
 * ThreadDetailPage Component
 * Trang chi tiết thread
 */

import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useThreadDetail } from '../hooks/useThreadDetail';
import { useThreadManagement } from '../hooks/useThreadManagement';
import { useOptimisticUpdateThread } from '../hooks/useThreadMutations';
import { Icon, ConfirmDeleteModal } from '@/shared/components/common';

/**
 * Component trang chi tiết thread
 */
export const ThreadDetailPage: React.FC = () => {
  const { threadId } = useParams<{ threadId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);


  // Hooks
  const { data: thread, isLoading, error } = useThreadDetail(threadId);
  const { optimisticUpdate, isLoading: isUpdating } = useOptimisticUpdateThread();
  const {
    deleteThread,
    isThreadActive,
    isThreadStarting,
    isThreadDeleting
  } = useThreadManagement({
    onThreadDeleted: () => {
      // Redirect về threads list khi thread bị xóa
      navigate('/threads');
    }
  });

  // Set edit name khi thread data load
  React.useEffect(() => {
    if (thread && !editName) {
      setEditName(thread.name);
    }
  }, [thread, editName]);

  // Xử lý back navigation
  const handleBack = () => {
    navigate('/threads');
  };

  // Xử lý delete thread
  const handleDelete = () => {
    if (threadId) {
      setShowDeleteModal(true);
    }
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = () => {
    setShowDeleteModal(false);
    if (threadId) {
      deleteThread(threadId);
    }
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
  };

  // Xử lý save name
  const handleSaveName = async () => {
    if (threadId && editName.trim() && editName.trim() !== thread?.name) {
      try {
        await optimisticUpdate(threadId, editName.trim());
        setIsEditing(false);
      } catch (error) {
        console.error('Failed to update thread name:', error);
      }
    } else {
      setIsEditing(false);
      setEditName(thread?.name || '');
    }
  };

  // Xử lý cancel edit
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditName(thread?.name || '');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-red-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            {t('threads:status.loading', 'Đang tải...')}
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !thread) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <Icon name="alert-circle" size="xl" />
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('threads:errors.loadDetail', 'Không thể tải thông tin chi tiết cuộc hội thoại')}
          </p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </div>
    );
  }

  const isActive = threadId ? isThreadActive(threadId) : false;
  const isStarting = threadId ? isThreadStarting(threadId) : false;
  const isDeleting = threadId ? isThreadDeleting(threadId) : false;

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div>
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBack}
                className="p-2 rounded-lg transition-colors"
              >
                <Icon name="arrow-left" size="lg" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {t('threads:threads.detail.title', 'Chi tiết cuộc hội thoại')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {thread.threadId}
                </p>
              </div>
            </div>

            {/* Status indicator */}
            {isActive && (
              <div className="flex items-center gap-2 px-3 py-1 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-full">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">
                  {t('threads:status.active', 'Đang hoạt động')}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="rounded-xl shadow-sm">
          <div className="p-6">
            {/* Thread Name */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('threads:threads.detail.name', 'Tên cuộc hội thoại')}
              </label>
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={editName}
                    onChange={(e) => setEditName(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                             bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                             focus:outline-none focus:ring-2 focus:ring-red-500"
                    autoFocus
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveName();
                      if (e.key === 'Escape') handleCancelEdit();
                    }}
                  />
                  <button
                    onClick={handleSaveName}
                    disabled={isUpdating}
                    className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
                  >
                    <Icon name="check" size="sm" />
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="p-2 text-red-600 hover:text-red-700"
                  >
                    <Icon name="x" size="sm" />
                  </button>
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {thread.name}
                  </h2>
                  <button
                    onClick={() => setIsEditing(true)}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Icon name="edit" size="sm" />
                  </button>
                </div>
              )}
            </div>

            {/* Thread Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('threads:threads.detail.threadId', 'Thread ID')}
                </label>
                <p className="text-gray-600 dark:text-gray-400 font-mono text-sm break-all">
                  {thread.threadId}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('threads:threads.detail.createdAt', 'Ngày tạo')}
                </label>
                <p className="text-gray-600 dark:text-gray-400">
                  {new Date(thread.createdAt).toLocaleString('vi-VN')}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('threads:threads.detail.updatedAt', 'Cập nhật lần cuối')}
                </label>
                <p className="text-gray-600 dark:text-gray-400">
                  {new Date(thread.updatedAt).toLocaleString('vi-VN')}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleDelete}
                disabled={isStarting || isDeleting}
                className="flex items-center gap-2 px-6 py-3 bg-red-500 text-white rounded-lg 
                         hover:bg-red-600 transition-colors disabled:opacity-50"
              >
                {isDeleting ? (
                  <Icon name="loading" size="sm" className="animate-spin" />
                ) : (
                  <Icon name="trash" size="sm" />
                )}
                <span>
                  {isDeleting
                    ? t('threads:status.deleting', 'Đang xóa...')
                    : t('threads:threads.item.actions.delete', 'Xóa cuộc hội thoại')
                  }
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Confirm Delete Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('threads:threads.item.confirmDelete', 'Xác nhận xóa cuộc hội thoại')}
        message={t('threads:threads.item.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa cuộc hội thoại "{{name}}"?', { name: thread?.name })}
        itemName={thread?.name}
        isSubmitting={isDeleting}
        confirmButtonText={t('threads:threads.item.actions.delete', 'Xóa')}
        cancelButtonText={t('common:cancel', 'Hủy')}
      />
    </div>
  );
};
