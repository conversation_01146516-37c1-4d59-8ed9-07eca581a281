# Thread Delete Race Condition Fix

## Vấn đề

Khi thực hiện xóa thread, có các vấn đề sau:
1. **API call sửa với ID cũ**: `currentThreadId` chưa được update kịp thời
2. **Danh sách chưa có thread nào được select**: State synchronization không đúng
3. **Race condition**: Nhiều source of truth cho `currentThreadId`

## Nguyên nhân

### 1. Multiple Sources of Truth
- `useThreadManagement` có `currentThreadId` riêng
- `threadIntegration.currentThreadId` từ Redux
- `activeThreadId` từ props của parent component

### 2. State Update Timing
- `setCurrentThreadId` trong mutation `onSuccess` chạy sau khi invalidate queries
- Redux state update không sync với local state
- Component re-render không theo đúng thứ tự

### 3. Hook Conflicts
- `ThreadsGrid` sử dụng `useThreadManagement` 
- `ThreadsPage` truyền `activeThreadId` từ Redux
- Không có sync mechanism giữa các state này

## Giải pháp

### 1. Unified Hook Usage

**Trước:**
```typescript
// ThreadsGrid sử dụng useThreadManagement
const { currentThreadId, ... } = useThreadManagement({
  availableThreads: threads,
  onThreadStarted,
  onThreadDeleted
});
```

**Sau:**
```typescript
// ThreadsGrid sử dụng useThreadManagementWithIntegration
const { 
  currentThreadId, 
  forceUpdateCurrentThread,
  ... 
} = useThreadManagementWithIntegration({
  availableThreads: threads,
  currentActiveThreadId: activeThreadId || null, // Sync với parent
  onThreadStarted,
  onThreadDeleted
});
```

### 2. Enhanced Delete Logic

**Trước:**
```typescript
// Chỉ xử lý khi có threads khác
if (threadId === currentThreadId && availableThreads.length > 1) {
  // Find next thread
}
```

**Sau:**
```typescript
if (threadId === currentThreadId) {
  const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);
  
  if (remainingThreads.length > 0) {
    // Switch to existing thread
    nextThreadId = sortedThreads[0]?.threadId;
    await externalChatStream.switchToThread(nextThreadId);
  } else {
    // Create new thread
    const newThread = await externalChatStream.createNewThread('New Chat');
    nextThreadId = newThread.threadId;
    isNewThreadCreated = true;
  }
}
```

### 3. Immediate State Update

**Trước:**
```typescript
onSuccess: ({ deletedThreadId, nextThreadId }) => {
  // Invalidate queries first
  queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
  
  // Update state after
  setCurrentThreadId(nextThreadId);
}
```

**Sau:**
```typescript
onSuccess: ({ deletedThreadId, nextThreadId, isNewThreadCreated }) => {
  // Update state IMMEDIATELY để tránh race condition
  setCurrentThreadId(nextThreadId);
  setDeletingThreadId(null);
  
  // Invalidate queries AFTER state update
  queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
}
```

### 4. Force Update Mechanism

Thêm function để force update khi có conflict:

```typescript
const forceUpdateCurrentThread = useCallback((threadId: string | null) => {
  console.log('[useThreadManagementWithIntegration] Force updating currentThreadId:', {
    from: currentThreadId,
    to: threadId
  });
  setCurrentThreadId(threadId);
}, [currentThreadId]);
```

### 5. Redux Sync Logic

```typescript
// Listen to Redux changes và force update nếu cần
useEffect(() => {
  if (threadIntegration.currentThreadId !== currentThreadId) {
    console.log('[ThreadsGrid] Force updating currentThreadId from Redux');
    forceUpdateCurrentThread(threadIntegration.currentThreadId);
  }
}, [threadIntegration.currentThreadId, currentThreadId, forceUpdateCurrentThread]);
```

## Luồng hoạt động mới

### Khi xóa thread:

1. **Preparation Phase**
   - Log detailed info về thread đang xóa
   - Identify remaining threads
   - Determine next action (switch/create)

2. **API Phase**
   - Call delete API
   - Handle external chat stream operations
   - Create new thread if needed

3. **State Update Phase**
   - Update local state IMMEDIATELY
   - Clear loading states
   - Log operation results

4. **Sync Phase**
   - Invalidate React Query cache
   - Trigger parent callbacks
   - Force update components if needed

## Logging Enhancement

Thêm detailed logging để debug:

```typescript
console.log('[useThreadManagementWithIntegration] Starting delete process:', {
  threadId,
  currentThreadId,
  availableThreadsCount: availableThreads.length,
  isCurrentThread: threadId === currentThreadId
});
```

## Kết quả

- ✅ Không còn race condition giữa các state
- ✅ `currentThreadId` được update đúng thứ tự
- ✅ UI sync đúng với backend state
- ✅ Auto-create thread khi xóa thread cuối cùng
- ✅ Detailed logging để debug issues

## Testing

Để test fix này:

1. Tạo một thread duy nhất
2. Xóa thread đó
3. Verify thread mới được tạo và selected
4. Verify không có API calls với ID cũ
5. Verify UI hiển thị đúng thread active
