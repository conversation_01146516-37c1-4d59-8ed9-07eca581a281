/**
 * useVoiceChat Hook
 * Tích hợp Speech-to-Text và Text-to-Speech cho voice chat functionality
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useSpeechToText, UseSpeechToTextOptions } from './useSpeechToText';
import { useTextToSpeech, UseTextToSpeechOptions } from './useTextToSpeech';
import { getVoiceConfigService } from '@/shared/services/voice-config.service';

/**
 * Voice chat mode
 */
export type VoiceChatMode = 'input' | 'output' | 'both' | 'disabled';

/**
 * Voice chat state
 */
export interface VoiceChatState {
  mode: VoiceChatMode;
  isRecording: boolean;
  isPlaying: boolean;
  isSynthesizing: boolean;
  currentTranscript: string;
  finalTranscript: string;
  currentSpeech: string;
  error: string | null;
  isEnabled: boolean;
  isConfigured: boolean;
}

/**
 * Voice chat options
 */
export interface VoiceChatOptions {
  mode?: VoiceChatMode;
  autoPlayResponses?: boolean;
  autoStopRecording?: boolean;
  speechToTextOptions?: UseSpeechToTextOptions;
  textToSpeechOptions?: UseTextToSpeechOptions;
  onVoiceInput?: (transcript: string, isFinal: boolean) => void;
  onVoiceOutput?: (text: string) => void;
  onError?: (error: Error, source: 'stt' | 'tts' | 'config') => void;
}

/**
 * Voice chat return type
 */
export interface VoiceChatReturn {
  // State
  state: VoiceChatState;
  
  // Voice Input (Speech-to-Text)
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  toggleRecording: () => Promise<void>;
  clearTranscript: () => void;
  
  // Voice Output (Text-to-Speech)
  speak: (text: string) => Promise<void>;
  stopSpeaking: () => void;
  clearSpeechQueue: () => void;
  
  // Configuration
  setMode: (mode: VoiceChatMode) => void;
  isAvailable: boolean;
  requestPermissions: () => Promise<boolean>;
}

/**
 * useVoiceChat Hook
 */
export function useVoiceChat(options: VoiceChatOptions = {}): VoiceChatReturn {
  const {
    mode = 'both',
    autoPlayResponses = true,
    autoStopRecording = true,
    speechToTextOptions = {},
    textToSpeechOptions = {},
    onVoiceInput,
    onVoiceOutput,
    onError,
  } = options;

  // State
  const [currentMode, setCurrentMode] = useState<VoiceChatMode>(mode);
  const [error, setError] = useState<string | null>(null);
  
  // Refs
  const configService = getVoiceConfigService();
  const supervisorTextBuffer = useRef<string>('');
  const isCollectingResponse = useRef<boolean>(false);

  // Voice configuration
  const voiceConfig = configService.getConfig();
  const isEnabled = configService.isEnabled();
  const isConfigured = configService.isConfigured();

  // Speech-to-Text hook
  const speechToText = useSpeechToText({
    continuous: true,
    interimResults: true,
    maxDuration: voiceConfig.maxRecordingDuration,
    silenceTimeout: voiceConfig.silenceTimeout,
    ...speechToTextOptions,
    onTranscript: (transcript, isFinal) => {
      console.log('[useVoiceChat] Voice input:', transcript, 'isFinal:', isFinal);
      onVoiceInput?.(transcript, isFinal);
      speechToTextOptions.onTranscript?.(transcript, isFinal);
    },
    onError: (error) => {
      console.error('[useVoiceChat] Speech-to-Text error:', error);
      setError(error.message);
      onError?.(error, 'stt');
      speechToTextOptions.onError?.(error);
    },
    onStart: () => {
      console.log('[useVoiceChat] Recording started');
      speechToTextOptions.onStart?.();
    },
    onEnd: () => {
      console.log('[useVoiceChat] Recording ended');
      speechToTextOptions.onEnd?.();
    },
  });

  // Text-to-Speech hook
  const textToSpeech = useTextToSpeech({
    autoPlay: autoPlayResponses,
    queueMode: true,
    ...textToSpeechOptions,
    onSynthesisStart: (text) => {
      console.log('[useVoiceChat] Voice output synthesis started:', text.substring(0, 50) + '...');
      onVoiceOutput?.(text);
      textToSpeechOptions.onSynthesisStart?.(text);
    },
    onPlaybackStart: () => {
      console.log('[useVoiceChat] Voice output playback started');
      textToSpeechOptions.onPlaybackStart?.();
    },
    onPlaybackEnd: () => {
      console.log('[useVoiceChat] Voice output playback ended');
      textToSpeechOptions.onPlaybackEnd?.();
    },
    onError: (error) => {
      console.error('[useVoiceChat] Text-to-Speech error:', error);
      setError(error.message);
      onError?.(error, 'tts');
      textToSpeechOptions.onError?.(error);
    },
  });

  // Check availability
  const isAvailable = speechToText.isAvailable && textToSpeech.isAvailable;

  // Combined state
  const state: VoiceChatState = {
    mode: currentMode,
    isRecording: speechToText.state.isRecording,
    isPlaying: textToSpeech.state.isPlaying,
    isSynthesizing: textToSpeech.state.isSynthesizing,
    currentTranscript: speechToText.state.transcript,
    finalTranscript: speechToText.state.finalTranscript,
    currentSpeech: textToSpeech.state.currentText,
    error: error || speechToText.state.error || textToSpeech.state.error,
    isEnabled,
    isConfigured,
  };

  // Voice Input Actions
  const startRecording = useCallback(async () => {
    if (currentMode === 'output' || currentMode === 'disabled') {
      console.warn('[useVoiceChat] Voice input is disabled in current mode');
      return;
    }

    if (!isConfigured) {
      const configError = new Error('Voice chat is not properly configured');
      setError(configError.message);
      onError?.(configError, 'config');
      return;
    }

    try {
      setError(null);
      await speechToText.startRecording();
    } catch (error) {
      console.error('[useVoiceChat] Failed to start recording:', error);
      const errorObj = error instanceof Error ? error : new Error('Failed to start recording');
      setError(errorObj.message);
      onError?.(errorObj, 'stt');
    }
  }, [currentMode, isConfigured, speechToText, onError]);

  const stopRecording = useCallback(async () => {
    try {
      await speechToText.stopRecording();
    } catch (error) {
      console.error('[useVoiceChat] Failed to stop recording:', error);
      const errorObj = error instanceof Error ? error : new Error('Failed to stop recording');
      setError(errorObj.message);
      onError?.(errorObj, 'stt');
    }
  }, [speechToText, onError]);

  const toggleRecording = useCallback(async () => {
    if (speechToText.state.isRecording) {
      await stopRecording();
    } else {
      await startRecording();
    }
  }, [speechToText.state.isRecording, startRecording, stopRecording]);

  const clearTranscript = useCallback(() => {
    speechToText.clearTranscript();
    setError(null);
  }, [speechToText]);

  // Voice Output Actions
  const speak = useCallback(async (text: string) => {
    if (currentMode === 'input' || currentMode === 'disabled') {
      console.warn('[useVoiceChat] Voice output is disabled in current mode');
      return;
    }

    if (!isConfigured) {
      const configError = new Error('Voice chat is not properly configured');
      setError(configError.message);
      onError?.(configError, 'config');
      return;
    }

    try {
      setError(null);
      await textToSpeech.speak(text);
    } catch (error) {
      console.error('[useVoiceChat] Failed to speak text:', error);
      const errorObj = error instanceof Error ? error : new Error('Failed to speak text');
      setError(errorObj.message);
      onError?.(errorObj, 'tts');
    }
  }, [currentMode, isConfigured, textToSpeech, onError]);

  const stopSpeaking = useCallback(() => {
    textToSpeech.stop();
  }, [textToSpeech]);

  const clearSpeechQueue = useCallback(() => {
    textToSpeech.clearQueue();
  }, [textToSpeech]);

  // Configuration Actions
  const setMode = useCallback((newMode: VoiceChatMode) => {
    console.log('[useVoiceChat] Mode changed from', currentMode, 'to', newMode);
    
    // Stop current activities when changing mode
    if (newMode === 'input' || newMode === 'disabled') {
      stopSpeaking();
      clearSpeechQueue();
    }
    
    if (newMode === 'output' || newMode === 'disabled') {
      stopRecording();
    }
    
    setCurrentMode(newMode);
    setError(null);
  }, [currentMode, stopSpeaking, clearSpeechQueue, stopRecording]);

  // Request permissions
  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      const hasPermission = await speechToText.requestPermission();
      if (!hasPermission) {
        const permissionError = new Error('Microphone permission is required for voice input');
        setError(permissionError.message);
        onError?.(permissionError, 'stt');
      }
      return hasPermission;
    } catch (error) {
      console.error('[useVoiceChat] Failed to request permissions:', error);
      const errorObj = error instanceof Error ? error : new Error('Failed to request permissions');
      setError(errorObj.message);
      onError?.(errorObj, 'stt');
      return false;
    }
  }, [speechToText, onError]);

  // Auto-stop recording when auto-stop is enabled and final transcript is received
  useEffect(() => {
    if (autoStopRecording && speechToText.state.finalTranscript && speechToText.state.isRecording) {
      console.log('[useVoiceChat] Auto-stopping recording due to final transcript');
      stopRecording();
    }
  }, [autoStopRecording, speechToText.state.finalTranscript, speechToText.state.isRecording, stopRecording]);

  // Clear error when configuration changes
  useEffect(() => {
    if (isConfigured && error?.includes('not properly configured')) {
      setError(null);
    }
  }, [isConfigured, error]);

  return {
    state,
    startRecording,
    stopRecording,
    toggleRecording,
    clearTranscript,
    speak,
    stopSpeaking,
    clearSpeechQueue,
    setMode,
    isAvailable,
    requestPermissions,
  };
}

export default useVoiceChat;
