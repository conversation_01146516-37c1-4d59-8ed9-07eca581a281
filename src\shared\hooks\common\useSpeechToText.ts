/**
 * useSpeechToText Hook
 * React hook để sử dụng Speech-to-Text functionality
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { SpeechToTextService, TranscriptionResult, SpeechRecognitionCallbacks } from '@/shared/services/speech-to-text.service';
import { getVoiceConfigService } from '@/shared/services/voice-config.service';

/**
 * Speech-to-Text hook state
 */
export interface UseSpeechToTextState {
  isRecording: boolean;
  isInitializing: boolean;
  transcript: string;
  finalTranscript: string;
  confidence: number;
  error: string | null;
  isSupported: boolean;
  permissionStatus: 'granted' | 'denied' | 'prompt' | 'unknown';
}

/**
 * Speech-to-Text hook options
 */
export interface UseSpeechToTextOptions {
  autoStart?: boolean;
  continuous?: boolean;
  interimResults?: boolean;
  maxDuration?: number; // seconds
  silenceTimeout?: number; // seconds
  onTranscript?: (transcript: string, isFinal: boolean) => void;
  onError?: (error: Error) => void;
  onStart?: () => void;
  onEnd?: () => void;
}

/**
 * Speech-to-Text hook return type
 */
export interface UseSpeechToTextReturn {
  // State
  state: UseSpeechToTextState;
  
  // Actions
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  toggleRecording: () => Promise<void>;
  clearTranscript: () => void;
  
  // Utils
  isAvailable: boolean;
  requestPermission: () => Promise<boolean>;
}

/**
 * useSpeechToText Hook
 */
export function useSpeechToText(options: UseSpeechToTextOptions = {}): UseSpeechToTextReturn {
  const {
    autoStart = false,
    continuous = true,
    interimResults = true,
    maxDuration = 60,
    silenceTimeout = 3,
    onTranscript,
    onError,
    onStart,
    onEnd,
  } = options;

  // State
  const [state, setState] = useState<UseSpeechToTextState>({
    isRecording: false,
    isInitializing: false,
    transcript: '',
    finalTranscript: '',
    confidence: 0,
    error: null,
    isSupported: false,
    permissionStatus: 'unknown',
  });

  // Refs
  const serviceRef = useRef<SpeechToTextService | null>(null);
  const silenceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const maxDurationTimerRef = useRef<NodeJS.Timeout | null>(null);
  const configService = getVoiceConfigService();

  // Check browser support
  const isAvailable = !!(
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia &&
    window.MediaRecorder
  );

  // Initialize service
  const initializeService = useCallback(async () => {
    if (!isAvailable) {
      setState(prev => ({ 
        ...prev, 
        isSupported: false, 
        error: 'Speech recognition is not supported in this browser' 
      }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, isInitializing: true, error: null }));

      const config = configService.getSpeechToTextConfig();
      
      if (!config.projectId || !config.apiKey) {
        throw new Error('Google Cloud credentials not configured');
      }

      serviceRef.current = new SpeechToTextService(config);
      
      setState(prev => ({ 
        ...prev, 
        isInitializing: false, 
        isSupported: true 
      }));

      return true;

    } catch (error) {
      console.error('[useSpeechToText] Failed to initialize service:', error);
      setState(prev => ({ 
        ...prev, 
        isInitializing: false, 
        isSupported: false, 
        error: error instanceof Error ? error.message : 'Failed to initialize speech recognition' 
      }));
      return false;
    }
  }, [isAvailable, configService]);

  // Request microphone permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Stop immediately after getting permission
      
      setState(prev => ({ ...prev, permissionStatus: 'granted' }));
      return true;

    } catch (error) {
      console.error('[useSpeechToText] Permission denied:', error);
      setState(prev => ({ 
        ...prev, 
        permissionStatus: 'denied',
        error: 'Microphone permission denied'
      }));
      return false;
    }
  }, []);

  // Clear silence timer
  const clearSilenceTimer = useCallback(() => {
    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }
  }, []);

  // Clear max duration timer
  const clearMaxDurationTimer = useCallback(() => {
    if (maxDurationTimerRef.current) {
      clearTimeout(maxDurationTimerRef.current);
      maxDurationTimerRef.current = null;
    }
  }, []);

  // Setup callbacks for speech service
  const setupCallbacks = useCallback((): SpeechRecognitionCallbacks => {
    return {
      onStart: () => {
        console.log('[useSpeechToText] Recording started');
        setState(prev => ({ 
          ...prev, 
          isRecording: true, 
          error: null 
        }));

        // Start max duration timer
        if (maxDuration > 0) {
          maxDurationTimerRef.current = setTimeout(() => {
            console.log('[useSpeechToText] Max duration reached, stopping recording');
            stopRecording();
          }, maxDuration * 1000);
        }

        onStart?.();
      },

      onResult: (result: TranscriptionResult) => {
        console.log('[useSpeechToText] Transcription result:', result);

        setState(prev => ({
          ...prev,
          transcript: result.transcript,
          confidence: result.confidence,
          finalTranscript: result.isFinal ? result.transcript : prev.finalTranscript,
        }));

        // Clear silence timer on new result
        clearSilenceTimer();

        // Setup new silence timer if continuous and interim results
        if (continuous && interimResults && !result.isFinal) {
          silenceTimerRef.current = setTimeout(() => {
            console.log('[useSpeechToText] Silence timeout reached, stopping recording');
            stopRecording();
          }, silenceTimeout * 1000);
        }

        // Call callback
        onTranscript?.(result.transcript, result.isFinal);
      },

      onError: (error: Error) => {
        console.error('[useSpeechToText] Recognition error:', error);
        setState(prev => ({ 
          ...prev, 
          isRecording: false, 
          error: error.message 
        }));
        
        clearSilenceTimer();
        clearMaxDurationTimer();
        onError?.(error);
      },

      onEnd: () => {
        console.log('[useSpeechToText] Recording ended');
        setState(prev => ({ ...prev, isRecording: false }));
        
        clearSilenceTimer();
        clearMaxDurationTimer();
        onEnd?.();
      },
    };
  }, [continuous, interimResults, maxDuration, silenceTimeout, onTranscript, onError, onStart, onEnd, clearSilenceTimer, clearMaxDurationTimer]);

  // Start recording
  const startRecording = useCallback(async () => {
    if (state.isRecording) {
      console.warn('[useSpeechToText] Already recording');
      return;
    }

    try {
      // Initialize service if needed
      if (!serviceRef.current) {
        const initialized = await initializeService();
        if (!initialized) return;
      }

      // Request permission if needed
      if (state.permissionStatus !== 'granted') {
        const hasPermission = await requestPermission();
        if (!hasPermission) return;
      }

      // Start recording
      const callbacks = setupCallbacks();
      await serviceRef.current!.startRecording(callbacks);

    } catch (error) {
      console.error('[useSpeechToText] Failed to start recording:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to start recording' 
      }));
      onError?.(error as Error);
    }
  }, [state.isRecording, state.permissionStatus, initializeService, requestPermission, setupCallbacks, onError]);

  // Stop recording
  const stopRecording = useCallback(async () => {
    if (!state.isRecording || !serviceRef.current) {
      return;
    }

    try {
      await serviceRef.current.stopRecording();
      clearSilenceTimer();
      clearMaxDurationTimer();

    } catch (error) {
      console.error('[useSpeechToText] Failed to stop recording:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to stop recording' 
      }));
      onError?.(error as Error);
    }
  }, [state.isRecording, clearSilenceTimer, clearMaxDurationTimer, onError]);

  // Toggle recording
  const toggleRecording = useCallback(async () => {
    if (state.isRecording) {
      await stopRecording();
    } else {
      await startRecording();
    }
  }, [state.isRecording, startRecording, stopRecording]);

  // Clear transcript
  const clearTranscript = useCallback(() => {
    setState(prev => ({
      ...prev,
      transcript: '',
      finalTranscript: '',
      confidence: 0,
    }));
  }, []);

  // Initialize on mount
  useEffect(() => {
    setState(prev => ({ ...prev, isSupported: isAvailable }));

    if (isAvailable && autoStart) {
      initializeService();
    }
  }, [isAvailable, autoStart, initializeService]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (serviceRef.current) {
        serviceRef.current.dispose();
      }
      clearSilenceTimer();
      clearMaxDurationTimer();
    };
  }, [clearSilenceTimer, clearMaxDurationTimer]);

  return {
    state,
    startRecording,
    stopRecording,
    toggleRecording,
    clearTranscript,
    isAvailable,
    requestPermission,
  };
}

export default useSpeechToText;
