# Thread Name Update Loop Fix

## 🐛 **Vấn đề**

C<PERSON> vòng lặp vô tận khi cập nhật tên thread do logic trong `ThreadsGrid` useEffect:

### Vòng lặp:
1. **Redux state change** → Trigger useEffect
2. **threadUpdateHook.optimisticUpdate()** → Update thread name  
3. **Thread name update** → Trigger Redux update lại
4. **Redux update** → Trigger useEffect lại → **Lặp lại từ bước 1**

### Nguyên nhân cụ thể:

#### 1. **Timestamp update không đúng chỗ**
```typescript
// ❌ TRƯỚC - Chỉ update timestamp khi có thread name change
if (updatedThreadId !== currentThreadId) {
  forceUpdateCurrentThread(updatedThreadId);
  // Thiếu: lastProcessedUpdate.current = threadIntegration.lastUpdated;
}

if (existingThread && existingThread.name !== updatedThreadName) {
  lastProcessedUpdate.current = threadIntegration.lastUpdated; // Chỉ update ở đây
  threadUpdateHook.optimisticUpdate(updatedThreadId, updatedThreadName);
}
```

#### 2. **Dependencies quá nhiều**
```typescript
// ❌ TRƯỚC - threads, threadUpdateHook thay đổi liên tục
}, [threadIntegration.lastUpdated, threadIntegration.currentThreadId, threadIntegration.currentThreadName, threads, threadUpdateHook, currentThreadId, forceUpdateCurrentThread]);
```

#### 3. **Không có debounce**
- Multiple rapid updates trigger multiple API calls
- Không có protection against excessive re-renders

---

## 🔧 **Giải pháp**

### 1. **Improved Logic Flow**

**Trước:**
```typescript
useEffect(() => {
  if (threadIntegration.lastUpdated > lastProcessedUpdate.current && ...) {
    // Process updates
    if (threadId !== currentThreadId) {
      forceUpdateCurrentThread(threadId); // ❌ Không update timestamp
    }
    
    if (existingThread && existingThread.name !== updatedName) {
      lastProcessedUpdate.current = threadIntegration.lastUpdated; // ❌ Chỉ update ở đây
      threadUpdateHook.optimisticUpdate(threadId, updatedName);
    }
  }
}, [/* too many dependencies */]);
```

**Sau:**
```typescript
useEffect(() => {
  // Early returns để tránh unnecessary processing
  if (threadIntegration.lastUpdated <= lastProcessedUpdate.current) {
    return; // Skip if already processed
  }

  if (!updatedThreadId || !updatedThreadName) {
    return; // Early return if no valid data
  }

  let hasChanges = false;

  // Force update current thread nếu cần
  if (updatedThreadId !== currentThreadId) {
    forceUpdateCurrentThread(updatedThreadId);
    hasChanges = true;
  }

  // Check thread name update
  const existingThread = threads.find(t => t.threadId === updatedThreadId);
  if (existingThread && existingThread.name !== updatedThreadName) {
    // ✅ Update timestamp BEFORE calling optimisticUpdate để tránh loop
    lastProcessedUpdate.current = threadIntegration.lastUpdated;
    
    memoizedOptimisticUpdate(updatedThreadId, updatedThreadName);
    hasChanges = true;
  }

  // ✅ Update timestamp nếu có bất kỳ thay đổi nào
  if (hasChanges) {
    lastProcessedUpdate.current = threadIntegration.lastUpdated;
  }
}, [
  threadIntegration.lastUpdated,
  threadIntegration.currentThreadId,
  threadIntegration.currentThreadName,
  currentThreadId,
  forceUpdateCurrentThread,
  memoizedOptimisticUpdate
  // ✅ Removed: threads, threadUpdateHook để tránh excessive re-renders
]);
```

### 2. **Memoized Optimistic Update**

```typescript
const memoizedOptimisticUpdate = useCallback((threadId: string, newName: string) => {
  // ✅ Kiểm tra xem có cần update không
  const existingThread = threads.find(t => t.threadId === threadId);
  if (!existingThread || existingThread.name === newName) {
    console.log('[ThreadsGrid] Skipping optimistic update - no changes needed');
    return;
  }

  // ✅ Clear previous debounce timer
  if (debounceTimerRef.current) {
    clearTimeout(debounceTimerRef.current);
  }

  // ✅ Debounce để tránh gọi API quá nhiều
  debounceTimerRef.current = setTimeout(() => {
    console.log('[ThreadsGrid] Performing debounced optimistic update:', { threadId, newName });
    threadUpdateHook.optimisticUpdate(threadId, newName);
  }, 100); // 100ms debounce
}, [threads, threadUpdateHook]);
```

### 3. **Debounce Mechanism**

```typescript
// Debounce timer để tránh gọi API quá nhiều
const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

// Cleanup debounce timer on unmount
useEffect(() => {
  return () => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
  };
}, []);
```

### 4. **Reduced Dependencies**

**Trước:**
```typescript
}, [threadIntegration.lastUpdated, threadIntegration.currentThreadId, threadIntegration.currentThreadName, threads, threadUpdateHook, currentThreadId, forceUpdateCurrentThread]);
```

**Sau:**
```typescript
}, [
  threadIntegration.lastUpdated,
  threadIntegration.currentThreadId,
  threadIntegration.currentThreadName,
  currentThreadId,
  forceUpdateCurrentThread,
  memoizedOptimisticUpdate
  // ❌ Removed: threads, threadUpdateHook để tránh excessive re-renders
]);
```

---

## ✅ **Kết quả**

### 1. **Vòng lặp vô tận đã được fix**
- ✅ Timestamp được update đúng thời điểm
- ✅ Early returns tránh unnecessary processing
- ✅ Memoized functions tránh re-renders

### 2. **Performance cải thiện**
- ✅ Debounce 100ms tránh API calls quá nhiều
- ✅ Reduced dependencies tránh excessive re-renders
- ✅ Early validation checks

### 3. **Logic rõ ràng hơn**
- ✅ Clear separation giữa threadId update và threadName update
- ✅ Proper timestamp management
- ✅ Better logging để debug

### 4. **Robust error handling**
- ✅ Cleanup debounce timer on unmount
- ✅ Validation checks trước khi process
- ✅ Graceful handling của edge cases

---

## 🧪 **Testing**

### Scenarios để test:

1. **Normal thread name update**
   - Update thread name từ ChatPanel
   - Verify: Chỉ 1 API call, UI update smooth

2. **Rapid updates**
   - Update thread name nhiều lần liên tiếp
   - Verify: Debounce hoạt động, chỉ API call cuối cùng

3. **Thread switching**
   - Switch giữa các threads
   - Verify: Không có loop, state sync đúng

4. **Redux state changes**
   - External Redux updates
   - Verify: Component sync đúng, không có excessive re-renders

---

## 📊 **Metrics**

### Trước fix:
- ❌ 10+ API calls cho 1 thread name update
- ❌ Vòng lặp vô tận trong console logs
- ❌ UI lag do excessive re-renders

### Sau fix:
- ✅ 1 API call cho 1 thread name update
- ✅ Clean console logs, no loops
- ✅ Smooth UI performance

---

## 📁 **Files Modified**

1. `ThreadsGrid.tsx` - Main logic fix
   - Improved useEffect logic
   - Added memoized optimistic update
   - Added debounce mechanism
   - Reduced dependencies
   - Added cleanup logic

Tất cả changes đều backward compatible và không ảnh hưởng đến functionality khác.
