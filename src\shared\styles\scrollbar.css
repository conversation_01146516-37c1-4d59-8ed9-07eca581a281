/* Custom Scrollbar Styles */

/* Webkit browsers (Chrome, Safari, newer versions of Opera) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3); /* gray-400 with opacity */
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5); /* gray-400 with higher opacity on hover */
}

/* For dark mode */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.3); /* gray-600 with opacity */
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.5); /* gray-600 with higher opacity on hover */
}

/* Hide scrollbar when not in use, but maintain functionality */
.custom-scrollbar.auto-hide::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.custom-scrollbar.auto-hide:hover::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
}

.dark .custom-scrollbar.auto-hide:hover::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.3);
}

/* Completely hide scrollbar but maintain functionality */
.custom-scrollbar.invisible-scrollbar::-webkit-scrollbar,
.hide-scrollbar::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.custom-scrollbar.invisible-scrollbar::-webkit-scrollbar-thumb,
.hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
}

/* Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.custom-scrollbar.auto-hide {
  scrollbar-color: transparent transparent;
}

.custom-scrollbar.auto-hide:hover {
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.custom-scrollbar.invisible-scrollbar,
.hide-scrollbar {
  scrollbar-width: none;
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.3) transparent;
}

.dark .custom-scrollbar.auto-hide:hover {
  scrollbar-color: rgba(75, 85, 99, 0.3) transparent;
}

/* Special styles for chat panel to avoid conflict with resizer */
.chat-panel-scroll {
  padding-right: 6px; /* Add padding to avoid content being too close to the edge */
  padding-top: 0; /* Ensure no extra padding at top that might push content under header */
  margin-top: 0; /* Ensure no margin that might create space */
}

/* Scrollbar hide utility class */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Thinking box scrollbar - red theme */
.thinking-box-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.thinking-box-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thinking-box-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(239, 68, 68, 0.3); /* red-500 with opacity */
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.thinking-box-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(239, 68, 68, 0.5); /* red-500 with higher opacity on hover */
}

/* For dark mode */
.dark .thinking-box-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(248, 113, 113, 0.3); /* red-400 with opacity */
}

.dark .thinking-box-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(248, 113, 113, 0.5); /* red-400 with higher opacity on hover */
}

/* Firefox */
.thinking-box-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(239, 68, 68, 0.3) transparent;
}

.dark .thinking-box-scrollbar {
  scrollbar-color: rgba(248, 113, 113, 0.3) transparent;
}
