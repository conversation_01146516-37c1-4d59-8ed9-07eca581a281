/**
 * useTextToSpeech Hook
 * React hook để sử dụng Text-to-Speech functionality
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { TextToSpeechService, SynthesisResult, TextToSpeechCallbacks } from '@/shared/services/text-to-speech.service';
import { getVoiceConfigService } from '@/shared/services/voice-config.service';

/**
 * Text-to-Speech hook state
 */
export interface UseTextToSpeechState {
  isInitializing: boolean;
  isSynthesizing: boolean;
  isPlaying: boolean;
  queueLength: number;
  currentText: string;
  error: string | null;
  isSupported: boolean;
}

/**
 * Text-to-Speech hook options
 */
export interface UseTextToSpeechOptions {
  autoPlay?: boolean;
  queueMode?: boolean;
  onSynthesisStart?: (text: string) => void;
  onSynthesisComplete?: (result: SynthesisResult) => void;
  onPlaybackStart?: () => void;
  onPlaybackEnd?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Text-to-Speech hook return type
 */
export interface UseTextToSpeechReturn {
  // State
  state: UseTextToSpeechState;
  
  // Actions
  speak: (text: string) => Promise<void>;
  synthesize: (text: string) => Promise<SynthesisResult | null>;
  stop: () => void;
  clearQueue: () => void;
  
  // Utils
  isAvailable: boolean;
}

/**
 * useTextToSpeech Hook
 */
export function useTextToSpeech(options: UseTextToSpeechOptions = {}): UseTextToSpeechReturn {
  const {
    autoPlay = true,
    queueMode = true,
    onSynthesisStart,
    onSynthesisComplete,
    onPlaybackStart,
    onPlaybackEnd,
    onError,
  } = options;

  // State
  const [state, setState] = useState<UseTextToSpeechState>({
    isInitializing: false,
    isSynthesizing: false,
    isPlaying: false,
    queueLength: 0,
    currentText: '',
    error: null,
    isSupported: false,
  });

  // Refs
  const serviceRef = useRef<TextToSpeechService | null>(null);
  const configService = getVoiceConfigService();

  // Check browser support
  const isAvailable = !!(
    window.Audio &&
    window.fetch &&
    window.URL &&
    window.URL.createObjectURL
  );

  // Initialize service
  const initializeService = useCallback(async () => {
    if (!isAvailable) {
      setState(prev => ({ 
        ...prev, 
        isSupported: false, 
        error: 'Text-to-Speech is not supported in this browser' 
      }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, isInitializing: true, error: null }));

      const config = configService.getTextToSpeechConfig();
      
      if (!config.projectId || !config.apiKey) {
        throw new Error('Google Cloud credentials not configured');
      }

      serviceRef.current = new TextToSpeechService(config);
      
      setState(prev => ({ 
        ...prev, 
        isInitializing: false, 
        isSupported: true 
      }));

      return true;

    } catch (error) {
      console.error('[useTextToSpeech] Failed to initialize service:', error);
      setState(prev => ({ 
        ...prev, 
        isInitializing: false, 
        isSupported: false, 
        error: error instanceof Error ? error.message : 'Failed to initialize text-to-speech' 
      }));
      return false;
    }
  }, [isAvailable, configService]);

  // Setup callbacks for TTS service
  const setupCallbacks = useCallback((text: string): TextToSpeechCallbacks => {
    return {
      onSynthesisStart: (synthesisText: string) => {
        console.log('[useTextToSpeech] Synthesis started for:', synthesisText.substring(0, 50) + '...');
        setState(prev => ({ 
          ...prev, 
          isSynthesizing: true, 
          currentText: synthesisText,
          error: null 
        }));
        onSynthesisStart?.(synthesisText);
      },

      onSynthesisComplete: (result: SynthesisResult) => {
        console.log('[useTextToSpeech] Synthesis completed');
        setState(prev => ({ 
          ...prev, 
          isSynthesizing: false 
        }));
        onSynthesisComplete?.(result);
      },

      onPlaybackStart: () => {
        console.log('[useTextToSpeech] Playback started');
        setState(prev => ({ 
          ...prev, 
          isPlaying: true 
        }));
        onPlaybackStart?.();
      },

      onPlaybackEnd: () => {
        console.log('[useTextToSpeech] Playback ended');
        setState(prev => ({ 
          ...prev, 
          isPlaying: false,
          currentText: ''
        }));
        
        // Update queue length
        if (serviceRef.current) {
          setState(prev => ({ 
            ...prev, 
            queueLength: serviceRef.current!.queueLength 
          }));
        }
        
        onPlaybackEnd?.();
      },

      onError: (error: Error) => {
        console.error('[useTextToSpeech] TTS error:', error);
        setState(prev => ({ 
          ...prev, 
          isSynthesizing: false,
          isPlaying: false,
          error: error.message 
        }));
        onError?.(error);
      },
    };
  }, [onSynthesisStart, onSynthesisComplete, onPlaybackStart, onPlaybackEnd, onError]);

  // Speak text (synthesize + play)
  const speak = useCallback(async (text: string) => {
    if (!text.trim()) {
      console.warn('[useTextToSpeech] Empty text provided');
      return;
    }

    try {
      // Initialize service if needed
      if (!serviceRef.current) {
        const initialized = await initializeService();
        if (!initialized) return;
      }

      // Setup callbacks
      const callbacks = setupCallbacks(text);

      // Add to queue or speak immediately
      if (queueMode) {
        await serviceRef.current!.addToQueue({ text }, callbacks);
        
        // Update queue length
        setState(prev => ({ 
          ...prev, 
          queueLength: serviceRef.current!.queueLength 
        }));
      } else {
        // Stop current audio if not in queue mode
        serviceRef.current!.stopCurrentAudio();
        await serviceRef.current!.speak(text, callbacks);
      }

    } catch (error) {
      console.error('[useTextToSpeech] Failed to speak text:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to speak text' 
      }));
      onError?.(error as Error);
    }
  }, [initializeService, setupCallbacks, queueMode, onError]);

  // Synthesize text only (no playback)
  const synthesize = useCallback(async (text: string): Promise<SynthesisResult | null> => {
    if (!text.trim()) {
      console.warn('[useTextToSpeech] Empty text provided');
      return null;
    }

    try {
      // Initialize service if needed
      if (!serviceRef.current) {
        const initialized = await initializeService();
        if (!initialized) return null;
      }

      setState(prev => ({ 
        ...prev, 
        isSynthesizing: true, 
        currentText: text,
        error: null 
      }));

      const result = await serviceRef.current!.synthesize(text);
      
      setState(prev => ({ 
        ...prev, 
        isSynthesizing: false 
      }));

      return result;

    } catch (error) {
      console.error('[useTextToSpeech] Failed to synthesize text:', error);
      setState(prev => ({ 
        ...prev, 
        isSynthesizing: false,
        error: error instanceof Error ? error.message : 'Failed to synthesize text' 
      }));
      onError?.(error as Error);
      return null;
    }
  }, [initializeService, onError]);

  // Stop current audio
  const stop = useCallback(() => {
    if (serviceRef.current) {
      serviceRef.current.stopCurrentAudio();
      setState(prev => ({ 
        ...prev, 
        isPlaying: false,
        currentText: ''
      }));
    }
  }, []);

  // Clear queue
  const clearQueue = useCallback(() => {
    if (serviceRef.current) {
      serviceRef.current.clearQueue();
      setState(prev => ({ 
        ...prev, 
        queueLength: 0,
        isPlaying: false,
        currentText: ''
      }));
    }
  }, []);

  // Initialize on mount
  useEffect(() => {
    setState(prev => ({ ...prev, isSupported: isAvailable }));

    if (isAvailable) {
      initializeService();
    }
  }, [isAvailable, initializeService]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (serviceRef.current) {
        serviceRef.current.dispose();
      }
    };
  }, []);

  return {
    state,
    speak,
    synthesize,
    stop,
    clearQueue,
    isAvailable,
  };
}

export default useTextToSpeech;
