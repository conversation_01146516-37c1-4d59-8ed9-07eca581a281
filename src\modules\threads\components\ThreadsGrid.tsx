/**
 * ThreadsGrid Component
 * Grid layout với infinite scroll cho threads
 */

import { ThreadItem } from '@/shared/types/chat-streaming.types';
import React, { useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useThreadManagementWithIntegration } from '../hooks/useThreadManagementWithIntegration';
import { useSimpleThreadDelete } from '../hooks/useSimpleThreadDelete';
import { useActiveThreadDelete } from '../hooks/useActiveThreadDelete';
import { useThreadsInfinite } from '../hooks/useThreadsInfinite';
import { useOptimisticUpdateThreadWithEvents } from '../hooks/useOptimisticUpdateThreadWithEvents';
import { useAppDispatch, useAppSelector } from '@/shared/store';
import { updateThreadName } from '@/shared/store/slices/threadIntegrationSlice';

import { ThreadCard } from './ThreadCard';
import { ScrollToTopButton } from '@/shared';

interface ThreadsGridProps {
  /**
   * Search term để filter threads
   */
  searchTerm?: string;

  /**
   * Sort options
   */
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Page size cho infinite loading
   */
  pageSize?: number;

  /**
   * Callback khi thread được started
   */
  onThreadStarted?: (threadId: string) => void;

  /**
   * Callback khi thread được deleted
   */
  onThreadDeleted?: (threadId: string) => void;

  /**
   * Callback khi thread name được changed
   */
  onThreadNameChanged?: (threadId: string, newName: string) => void;

  /**
   * Enable chat integration
   */
  enableChatIntegration?: boolean;

  /**
   * Chat configuration (required if enableChatIntegration is true)
   */
  chatConfig?: {
    agentId?: string;
    apiBaseUrl?: string;
    sseBaseUrl?: string;
    getAuthToken?: () => string | Promise<string>;
    debug?: boolean;
  } | undefined;

  /**
   * Active thread ID để highlight
   */
  activeThreadId?: string | null;

  /**
   * External chat stream để xử lý active thread deletion
   */
  externalChatStream?: {
    switchToThread?: (threadId: string) => Promise<void>;
    createNewThread?: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  };
}

/**
 * Component grid hiển thị threads với infinite scroll
 */
export const ThreadsGrid: React.FC<ThreadsGridProps> = ({
  searchTerm = '',
  sortBy = 'updatedAt',
  sortDirection = 'DESC',
  pageSize = 20,
  onThreadStarted,
  onThreadDeleted,
  onThreadNameChanged,
  activeThreadId,
  externalChatStream
}) => {
  const { t } = useTranslation();
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const prevQueryRef = useRef({ searchTerm, sortBy, sortDirection });

  // Redux for thread integration
  const dispatch = useAppDispatch();
  const threadIntegration = useAppSelector(state => state.threadIntegration);

  // Use infinite query
  const {
    threads,
    totalItems,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
    isSearching
  } = useThreadsInfinite({
    pageSize,
    sortBy,
    sortDirection,
    searchTerm
  });

  // Debug logging cho search results
  console.log('[ThreadsGrid] Component state:', {
    searchTerm,
    sortBy,
    sortDirection,
    threadsCount: threads.length,
    totalItems,
    isLoading,
    isError,
    isSearching,
    hasNextPage,
    isFetchingNextPage,
    // Debug search results
    ...(isSearching && {
      searchDebug: {
        searchTerm,
        threadsData: threads.slice(0, 3).map(t => ({ id: t.threadId, name: t.name })),
        rawThreadsLength: threads.length
      }
    })
  });

  // Thread management hook cho start và view
  const {
    currentThreadId,
    startThread,
    viewThreadDetail,
    isThreadActive,
    isThreadStarting,
    forceUpdateCurrentThread
  } = useThreadManagementWithIntegration({
    availableThreads: threads,
    currentActiveThreadId: activeThreadId || null, // Sync với activeThreadId từ parent
    onThreadStarted: onThreadStarted || (() => { }),
    onThreadDeleted: onThreadDeleted || (() => { })
  });

  // Hook cho xóa thread đơn giản (non-active)
  const simpleDeleteHook = useSimpleThreadDelete({
    onThreadDeleted: (threadId) => {
      console.log('[ThreadsGrid] Simple thread deleted:', threadId);
      onThreadDeleted?.(threadId);
    }
  });

  // Hook cho xóa active thread với integration
  const activeDeleteHook = useActiveThreadDelete({
    availableThreads: threads,
    externalChatStream: externalChatStream,
    onThreadDeleted: (deletedThreadId, nextThreadId) => {
      console.log('[ThreadsGrid] Active thread deleted:', { deletedThreadId, nextThreadId });
      onThreadDeleted?.(deletedThreadId);
    }
  });

  // Thread update hook với events
  const threadUpdateHook = useOptimisticUpdateThreadWithEvents({
    onThreadNameChanged: (threadId, newName) => {
      console.log('[ThreadsGrid] Thread name changed locally:', { threadId, newName });

      // Only dispatch Redux if this is the current thread
      if (threadId === threadIntegration.currentThreadId) {
        console.log('[ThreadsGrid] Dispatching updateThreadName to Redux for current thread');
        dispatch(updateThreadName({ threadId, newName }));
      }

      // Call parent callback
      onThreadNameChanged?.(threadId, newName);
    },
    onError: (error) => {
      console.error('[ThreadsGrid] Thread update error:', error);
    }
  });

  // Memoized optimistic update với debounce để tránh re-renders và API calls
  const memoizedOptimisticUpdate = useCallback((threadId: string, newName: string) => {
    // Kiểm tra xem có cần update không
    const existingThread = threads.find(t => t.threadId === threadId);
    if (!existingThread || existingThread.name === newName) {
      console.log('[ThreadsGrid] Skipping optimistic update - no changes needed');
      return;
    }

    // Clear previous debounce timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Debounce để tránh gọi API quá nhiều
    debounceTimerRef.current = setTimeout(() => {
      console.log('[ThreadsGrid] Performing debounced optimistic update:', { threadId, newName });
      threadUpdateHook.optimisticUpdate(threadId, newName);
    }, 100); // 100ms debounce
  }, [threads, threadUpdateHook]);

  // Track last processed update để prevent infinite loop
  const lastProcessedUpdate = useRef(0);

  // Debounce timer để tránh gọi API quá nhiều
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Listen to Redux thread integration state changes
  useEffect(() => {
    // Only process if this is a new update and not from this component
    if (threadIntegration.lastUpdated <= lastProcessedUpdate.current) {
      return; // Skip if already processed
    }

    const updatedThreadId = threadIntegration.currentThreadId;
    const updatedThreadName = threadIntegration.currentThreadName;

    // Early return if no valid data
    if (!updatedThreadId || !updatedThreadName) {
      return;
    }

    let hasChanges = false;

    // Force update current thread nếu cần (với validation để tránh deleted threads)
    if (updatedThreadId !== currentThreadId) {
      console.log('[ThreadsGrid] Force updating currentThreadId from Redux:', {
        from: currentThreadId,
        to: updatedThreadId
      });

      // Kiểm tra xem thread có tồn tại trong current threads list không
      const threadExists = threads.find(t => t.threadId === updatedThreadId);
      if (threadExists) {
        forceUpdateCurrentThread(updatedThreadId);
        hasChanges = true;
      } else {
        console.warn('[ThreadsGrid] Skipping force update - thread not found in current list:', updatedThreadId);
      }
    }

    // Check if thread name needs update (chỉ khi thread tồn tại trong list)
    const existingThread = threads.find(t => t.threadId === updatedThreadId);
    if (existingThread && existingThread.name !== updatedThreadName) {

      // IMPORTANT: Update timestamp BEFORE calling optimisticUpdate để tránh loop
      lastProcessedUpdate.current = threadIntegration.lastUpdated;

      // Update thread name trong local list (không trigger thêm Redux updates)
      memoizedOptimisticUpdate(updatedThreadId, updatedThreadName);
      hasChanges = true;
    }

    // Update timestamp nếu có bất kỳ thay đổi nào
    if (hasChanges) {
      lastProcessedUpdate.current = threadIntegration.lastUpdated;
    }
  }, [
    threadIntegration.lastUpdated,
    threadIntegration.currentThreadId,
    threadIntegration.currentThreadName,
    currentThreadId,
    forceUpdateCurrentThread,
    memoizedOptimisticUpdate
  ]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Intersection Observer cho infinite scroll
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    if (entry && entry.isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    // Tìm scroll container
    const scrollContainer = document.getElementById('threads-scroll-container');

    const observer = new IntersectionObserver(handleIntersection, {
      root: scrollContainer, // Sử dụng scroll container làm root thay vì viewport
      threshold: 0.1,
      rootMargin: '100px'
    });

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    // Fallback scroll listener để đảm bảo loading hoạt động
    const handleScroll = () => {
      if (!scrollContainer || !hasNextPage || isFetchingNextPage) return;

      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      // Trigger khi scroll đến 90% cuối container
      if (scrollPercentage > 0.9) {
        fetchNextPage();
      }
    };

    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll, { passive: true });
    }

    return () => {
      observer.disconnect();
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [handleIntersection, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Effect để scroll về đầu khi query parameters thay đổi
  useEffect(() => {
    const prevQuery = prevQueryRef.current;
    const currentQuery = { searchTerm, sortBy, sortDirection };

    // Kiểm tra xem có parameter nào thay đổi không
    const hasQueryChanged =
      prevQuery.searchTerm !== currentQuery.searchTerm ||
      prevQuery.sortBy !== currentQuery.sortBy ||
      prevQuery.sortDirection !== currentQuery.sortDirection;

    if (hasQueryChanged) {
      // Scroll về đầu container
      const scrollContainer = document.getElementById('threads-scroll-container');
      if (scrollContainer) {
        scrollContainer.scrollTop = 0;
      }

      // Cập nhật ref
      prevQueryRef.current = currentQuery;
    }
  }, [searchTerm, sortBy, sortDirection]);


  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            {t('threads:status.loading', 'Đang tải...')}
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('threads:errors.loadThreads', 'Có lỗi xảy ra khi tải danh sách cuộc hội thoại')}
          </p>
          {error && (
            <div className="text-sm text-red-600 dark:text-red-400 mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <strong>Error:</strong> {error.message}
            </div>
          )}
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            {t('threads:status.retry', 'Thử lại')}
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (threads.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-300 dark:text-gray-600 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            {searchTerm
              ? t('threads:threads.list.noResults', 'Không tìm thấy cuộc hội thoại nào')
              : t('threads:threads.list.empty', 'Chưa có cuộc hội thoại nào')
            }
          </p>

          {/* Debug info khi search */}
          {searchTerm && process.env['NODE_ENV'] === 'development' && (
            <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left text-xs max-w-md mx-auto">
              <h4 className="font-bold mb-2">Debug Info:</h4>
              <pre className="whitespace-pre-wrap text-xs">
                {JSON.stringify({
                  searchTerm,
                  isLoading,
                  isError,
                  threadsLength: threads.length,
                  totalItems,
                  isSearching,
                  hasNextPage,
                  errorMessage: error?.message
                }, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Results info */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isSearching ? (
            t('threads:threads.list.search.resultsWithTerm', 'Tìm thấy {{count}} kết quả cho "{{term}}"', {
              count: threads.length, // Khi search, hiển thị số lượng threads đã filter
              term: searchTerm
            })
          ) : (
            t('threads:threads.list.search.results', 'Tìm thấy {{count}} kết quả', {
              count: totalItems // Khi không search, hiển thị tổng số từ API
            })
          )}
        </p>

        {(threadIntegration.currentThreadId || currentThreadId || activeThreadId) && (
          <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>
              {threadIntegration.currentThreadId
                ? t('threads:status.activeChatThread', 'Thread đang chat: {{name}}', {
                  name: threadIntegration.currentThreadName || threadIntegration.currentThreadId.slice(-8)
                })
                : activeThreadId
                  ? t('threads:status.activeChatThread', 'Thread đang chat: {{id}}', { id: activeThreadId.slice(-8) })
                  : t('threads:status.activeThread', 'Có thread đang hoạt động')
              }
            </span>
          </div>
        )}
      </div>

      {/* Threads Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 overflow-hidden">
        {threads.map((thread: ThreadItem) => (
          <ThreadCard
            key={thread.threadId}
            thread={thread}
            isActive={
              isThreadActive(thread.threadId) ||
              thread.threadId === activeThreadId ||
              thread.threadId === threadIntegration.currentThreadId
            }
            isStarting={isThreadStarting(thread.threadId)}
            isDeleting={
              simpleDeleteHook.isThreadDeleting(thread.threadId) ||
              activeDeleteHook.isThreadDeleting(thread.threadId)
            }
            onStart={startThread}
            onDelete={simpleDeleteHook.deleteThread}
            onDeleteActive={activeDeleteHook.deleteActiveThread}
            onClick={viewThreadDetail}
            externalUpdateHook={threadUpdateHook}
          />
        ))}
      </div>

      {/* Load More Trigger - Chỉ hiển thị khi không search */}
      {hasNextPage && !isSearching && (
        <div ref={loadMoreRef} className="flex items-center justify-center py-8">
          {isFetchingNextPage ? (
            <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
              <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span>{t('threads:status.loadingMore', 'Đang tải thêm...')}</span>
            </div>
          ) : (
            <button
              onClick={() => fetchNextPage()}
              className="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300
                       rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              {t('threads:actions.loadMore', 'Tải thêm')}
            </button>
          )}
        </div>
      )}

      {/* End indicator */}
      {!hasNextPage && threads.length > 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            {isSearching
              ? t('threads:status.searchComplete', 'Đã hiển thị tất cả kết quả tìm kiếm')
              : t('threads:status.allLoaded', 'Đã tải tất cả cuộc hội thoại')
            }
          </p>
        </div>
      )}

      {/* Scroll to Top Button - positioned at top center of threads area */}
      <ScrollToTopButton
        containerId="threads-scroll-container"
        showAfter={200}
        position={{ top: '30px', left: '50%', transform: 'translateX(-50%)' }}
        size="md"
      />
    </div>
  );
};
