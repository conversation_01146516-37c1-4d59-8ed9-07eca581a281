/**
 * Enhanced useThreadManagement với external chat stream integration
 * Hook quản lý thread operations với auto-switch logic khi delete
 */

import { useState, useCallback, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { ThreadsService } from '../services';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadItem } from '@/shared/types/chat-streaming.types';

interface UseThreadManagementWithIntegrationOptions {
  /**
   * Callback khi thread được load vào chat
   */
  onThreadStarted?: (threadId: string) => void;

  /**
   * Callback khi thread bị xóa
   */
  onThreadDeleted?: (threadId: string, nextThreadId?: string | null) => void;

  /**
   * Danh sách threads hiện tại (để tìm next thread khi delete)
   */
  availableThreads?: ThreadItem[];

  /**
   * External chat stream để integrate
   */
  externalChatStream?: {
    switchToThread?: (threadId: string) => Promise<void>;
    createNewThread?: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  };

  /**
   * Current active thread ID
   */
  currentActiveThreadId?: string | null;
}

/**
 * Hook quản lý thread operations với integration
 */
export const useThreadManagementWithIntegration = ({
  onThreadStarted,
  onThreadDeleted,
  availableThreads = [],
  externalChatStream,
  currentActiveThreadId
}: UseThreadManagementWithIntegrationOptions = {}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [currentThreadId, setCurrentThreadId] = useState<string | null>(currentActiveThreadId || null);
  const [startingThreadId, setStartingThreadId] = useState<string | null>(null);
  const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

  // Sync với external active thread ID
  useEffect(() => {
    if (currentActiveThreadId !== currentThreadId) {
      console.log('[useThreadManagementWithIntegration] Syncing currentThreadId:', {
        from: currentThreadId,
        to: currentActiveThreadId
      });
      setCurrentThreadId(currentActiveThreadId || null);
    }
  }, [currentActiveThreadId, currentThreadId]);

  // Mutation để start thread
  const startThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setStartingThreadId(threadId);
      
      // Nếu có external chat stream, sử dụng nó để switch thread
      if (externalChatStream && externalChatStream.switchToThread) {
        await externalChatStream.switchToThread(threadId);
      }
      
      return threadId;
    },
    onSuccess: (threadId) => {
      setCurrentThreadId(threadId);
      setStartingThreadId(null);
      onThreadStarted?.(threadId);
    },
    onError: (error) => {
      console.error('[useThreadManagementWithIntegration] Failed to start thread:', error);
      setStartingThreadId(null);
    },
  });

  // Mutation để delete thread với auto-switch logic
  const deleteThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setDeletingThreadId(threadId);

      console.log('[useThreadManagementWithIntegration] Starting delete process:', {
        threadId,
        currentThreadId,
        availableThreadsCount: availableThreads.length,
        isCurrentThread: threadId === currentThreadId
      });

      // Xóa thread từ API
      await ThreadsService.deleteThread(threadId);

      // Tìm next thread để auto-switch hoặc tạo mới nếu không có
      let nextThreadId: string | null = null;
      let isNewThreadCreated = false;

      if (threadId === currentThreadId) {
        const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);

        console.log('[useThreadManagementWithIntegration] Remaining threads after delete:', {
          remainingCount: remainingThreads.length,
          remainingThreads: remainingThreads.map(t => ({ id: t.threadId, name: t.name }))
        });

        if (remainingThreads.length > 0) {
          // Có threads khác, chọn thread mới nhất
          const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
          nextThreadId = sortedThreads[0]?.threadId || null;

          console.log('[useThreadManagementWithIntegration] Auto-switching to existing thread:', nextThreadId);

          // Auto-switch nếu có external chat stream
          if (externalChatStream && externalChatStream.switchToThread && nextThreadId) {
            try {
              await externalChatStream.switchToThread(nextThreadId);
            } catch (switchError) {
              console.error('[useThreadManagementWithIntegration] Failed to switch to next thread:', switchError);
              // Nếu switch thất bại, set nextThreadId = null
              nextThreadId = null;
            }
          }
        } else {
          // Không có thread nào còn lại, tạo thread mới
          console.log('[useThreadManagementWithIntegration] No threads remaining, creating new thread...');

          if (externalChatStream && externalChatStream.createNewThread) {
            try {
              const newThread = await externalChatStream.createNewThread('New Chat');
              nextThreadId = newThread.threadId;
              isNewThreadCreated = true;

              console.log('[useThreadManagementWithIntegration] Created new thread:', newThread);
            } catch (error) {
              console.error('[useThreadManagementWithIntegration] Failed to create new thread:', error);
              // Nếu tạo thread thất bại, để nextThreadId = null
              nextThreadId = null;
            }
          } else {
            console.warn('[useThreadManagementWithIntegration] No external chat stream available to create new thread');
          }
        }
      } else {
        console.log('[useThreadManagementWithIntegration] Deleted thread is not current thread, no auto-switch needed');
      }

      return { deletedThreadId: threadId, nextThreadId, isNewThreadCreated };
    },
    onSuccess: ({ deletedThreadId, nextThreadId, isNewThreadCreated }) => {
      console.log('[useThreadManagementWithIntegration] Delete mutation success:', {
        deletedThreadId,
        nextThreadId,
        isNewThreadCreated,
        previousCurrentThreadId: currentThreadId
      });

      // Update current thread IMMEDIATELY để tránh race condition
      setCurrentThreadId(nextThreadId);
      setDeletingThreadId(null);

      // Log thông tin về kết quả
      if (isNewThreadCreated) {
        console.log('[useThreadManagementWithIntegration] Thread deleted and new thread created:', {
          deletedThreadId,
          newThreadId: nextThreadId
        });
      } else if (nextThreadId) {
        console.log('[useThreadManagementWithIntegration] Thread deleted and switched to existing thread:', {
          deletedThreadId,
          nextThreadId
        });
      } else {
        console.log('[useThreadManagementWithIntegration] Thread deleted with no replacement:', {
          deletedThreadId
        });
      }

      // Remove deleted thread detail from cache để tránh 404 error
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

      // Force invalidate và refetch tất cả list queries
      console.log('[useThreadManagementWithIntegration] Invalidating queries after delete...');

      // Invalidate infinite queries
      queryClient.invalidateQueries({
        queryKey: ['threads', 'infinite'],
        refetchType: 'active'
      });

      // Invalidate list queries
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query) => {
          const key = query.queryKey;
          const isListQuery = key.includes('list') || key.includes('paginated') || key.includes('infinite');
          const isDetailQuery = key.includes('detail');
          return isListQuery && !isDetailQuery;
        },
        refetchType: 'active'
      });

      console.log('[useThreadManagementWithIntegration] Queries invalidated successfully');

      // Trigger callback với nextThreadId
      onThreadDeleted?.(deletedThreadId, nextThreadId);
    },
    onError: (error) => {
      console.error('[useThreadManagementWithIntegration] Failed to delete thread:', error);
      setDeletingThreadId(null);
    },
  });

  // Start thread function
  const startThread = useCallback((threadId: string) => {
    if (startingThreadId || deletingThreadId) {
      console.warn('[useThreadManagementWithIntegration] Operation in progress, skipping start thread');
      return;
    }
    startThreadMutation.mutate(threadId);
  }, [startThreadMutation, startingThreadId, deletingThreadId]);

  // Delete thread function
  const deleteThread = useCallback((threadId: string) => {
    if (startingThreadId || deletingThreadId) {
      console.warn('[useThreadManagementWithIntegration] Operation in progress, skipping delete thread');
      return;
    }
    deleteThreadMutation.mutate(threadId);
  }, [deleteThreadMutation, startingThreadId, deletingThreadId]);

  // Navigate to thread detail
  const viewThreadDetail = useCallback((threadId: string) => {
    // Prevent navigation nếu thread đang bị xóa
    if (deletingThreadId === threadId) {
      console.log('[useThreadManagementWithIntegration] Skipping navigation - thread is being deleted:', threadId);
      return;
    }

    // Prevent navigation nếu có operation đang chạy
    if (startingThreadId || deletingThreadId) {
      console.log('[useThreadManagementWithIntegration] Skipping navigation - operation in progress:', {
        startingThreadId, deletingThreadId, targetThreadId: threadId
      });
      return;
    }

    console.log('[useThreadManagementWithIntegration] Navigating to thread detail:', threadId);
    navigate(`/threads/${threadId}`);
  }, [navigate, deletingThreadId, startingThreadId]);

  // Force update current thread (để fix race condition)
  const forceUpdateCurrentThread = useCallback((threadId: string | null) => {
    console.log('[useThreadManagementWithIntegration] Force updating currentThreadId:', {
      from: currentThreadId,
      to: threadId
    });
    setCurrentThreadId(threadId);
  }, [currentThreadId]);

  // Check if thread is active
  const isThreadActive = useCallback((threadId: string) => {
    return currentThreadId === threadId;
  }, [currentThreadId]);

  // Check if thread is starting
  const isThreadStarting = useCallback((threadId: string) => {
    return startingThreadId === threadId;
  }, [startingThreadId]);

  // Check if thread is deleting
  const isThreadDeleting = useCallback((threadId: string) => {
    return deletingThreadId === threadId;
  }, [deletingThreadId]);

  // Get next thread for auto-switch preview
  const getNextThreadForAutoSwitch = useCallback((threadId: string) => {
    if (availableThreads.length <= 1) return null;
    
    const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);
    if (remainingThreads.length === 0) return null;
    
    const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
    return sortedThreads[0] || null;
  }, [availableThreads]);

  return {
    // State
    currentThreadId,
    startingThreadId,
    deletingThreadId,

    // Actions
    startThread,
    deleteThread,
    viewThreadDetail,
    forceUpdateCurrentThread,

    // Checkers
    isThreadActive,
    isThreadStarting,
    isThreadDeleting,

    // Utilities
    getNextThreadForAutoSwitch,

    // Loading states
    isStartingAnyThread: !!startingThreadId,
    isDeletingAnyThread: !!deletingThreadId,
  };
};
