/**
 * Hook để handle real-time R-Point updates từ SSE events
 */
import { useRPoint } from '@/shared/contexts/useRPoint';
import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

export interface RPointUpdateOptions {
  /**
   * <PERSON><PERSON> sử dụng updatedBalance thay vì trừ rPointCost không
   * - true: Sử dụng updatedBalance để set lại toàn bộ balance
   * - false: Trừ rPointCost từ balance hiện tại
   */
  useUpdatedBalance?: boolean;
  
  /**
   * Callback khi R-Point được cập nhật
   */
  onUpdate?: (newBalance: number, cost: number, timestamp: number) => void;
  
  /**
   * Callback khi có lỗi trong quá trình cập nhật
   */
  onError?: (error: string) => void;
}

/**
 * Hook để xử lý real-time R-Point updates
 */
export const useRPointUpdate = (options: RPointUpdateOptions = {}) => {
  const { userRPoints, setUserRPoints, deductPoints } = useRPoint();
  const { useUpdatedBalance = true, onUpdate, onError } = options;
  const queryClient = useQueryClient();

  /**
   * Handler cho SSE update_rpoint event
   */
  const handleRPointUpdate = useCallback((
    rPointCost: number, 
    updatedBalance: string, 
    timestamp: number
  ) => {
    console.log('[useRPointUpdate] Processing R-Point update:', {
      rPointCost,
      updatedBalance,
      timestamp,
      currentBalance: userRPoints,
      useUpdatedBalance
    });

    try {
      const newBalance = parseInt(updatedBalance, 10);
      
      // Validate updatedBalance
      if (isNaN(newBalance) || newBalance < 0) {
        const errorMsg = `Invalid updatedBalance: ${updatedBalance}`;
        console.error('[useRPointUpdate] Error:', errorMsg);
        onError?.(errorMsg);
        return;
      }

      if (useUpdatedBalance) {
        // Phương pháp 1: Sử dụng updatedBalance để set lại toàn bộ
        setUserRPoints(newBalance);
        console.log('[useRPointUpdate] ✅ R-Point updated:', userRPoints, '→', newBalance);

        // Invalidate API query để force refresh ViewHeader
        console.log('[useRPointUpdate] Invalidating query key: [user, points]');
        queryClient.invalidateQueries({ queryKey: ['user', 'points'] });

        // Also set query data directly for immediate update
        queryClient.setQueryData(['user', 'points'], newBalance);
        console.log('[useRPointUpdate] Set query data directly to:', newBalance);

        onUpdate?.(newBalance, rPointCost, timestamp);

      } else {
        // Phương pháp 2: Trừ rPointCost từ balance hiện tại
        const success = deductPoints(rPointCost);

        if (success) {
          const newCalculatedBalance = userRPoints - rPointCost;
          console.log('[useRPointUpdate] ✅ R-Point deducted:', rPointCost, 'New balance:', newCalculatedBalance);

          // Invalidate API query để force refresh ViewHeader
          console.log('[useRPointUpdate] Invalidating query key: [user, points]');
          queryClient.invalidateQueries({ queryKey: ['user', 'points'] });

          // Also set query data directly for immediate update
          queryClient.setQueryData(['user', 'points'], newCalculatedBalance);
          console.log('[useRPointUpdate] Set query data directly to:', newCalculatedBalance);

          onUpdate?.(newCalculatedBalance, rPointCost, timestamp);
        } else {
          const errorMsg = `Insufficient R-Point balance. Required: ${rPointCost}, Available: ${userRPoints}`;
          console.error('[useRPointUpdate] Error:', errorMsg);
          onError?.(errorMsg);
        }
      }
      
    } catch (error) {
      const errorMsg = `Failed to process R-Point update: ${error}`;
      console.error('[useRPointUpdate] Error:', errorMsg);
      onError?.(errorMsg);
    }
  }, [userRPoints, setUserRPoints, deductPoints, useUpdatedBalance, onUpdate, onError]);

  /**
   * Kiểm tra xem có đủ R-Point để thực hiện action không
   */
  const checkSufficientBalance = useCallback((requiredAmount: number): boolean => {
    return userRPoints >= requiredAmount;
  }, [userRPoints]);

  /**
   * Format R-Point number với thousand separators
   */
  const formatRPoint = useCallback((amount: number): string => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  }, []);

  return {
    // Current state
    currentBalance: userRPoints,
    
    // Handlers
    handleRPointUpdate,
    checkSufficientBalance,
    formatRPoint,
    
    // Manual operations (nếu cần)
    deductPoints,
    setBalance: setUserRPoints
  };
};

export default useRPointUpdate;
