/**
 * Enhanced useOptimisticUpdateThread với event callbacks
 * Hook để update thread name với optimistic updates và event emission
 */

import { UpdateThreadRequest, ThreadDetailResponse, GetThreadsResponse, ThreadItem } from '@/shared/types/chat-streaming.types';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadsService } from '../services';

interface UseOptimisticUpdateThreadWithEventsOptions {
  /**
   * Callback khi thread name được update thành công
   */
  onThreadNameChanged?: (threadId: string, newName: string) => void;

  /**
   * Callback khi có lỗi
   */
  onError?: (error: Error) => void;
}

/**
 * Hook để optimistic update thread name với event callbacks
 */
export const useOptimisticUpdateThreadWithEvents = (
  options: UseOptimisticUpdateThreadWithEventsOptions = {}
) => {
  const queryClient = useQueryClient();
  const { onThreadNameChanged, onError } = options;

  // Base update mutation
  const updateMutation = useMutation({
    mutationFn: ({ threadId, data }: { threadId: string; data: UpdateThreadRequest }) =>
      ThreadsService.updateThread(threadId, data),
    onSuccess: (updatedThread, { threadId }) => {
      // Update cache cho thread detail
      queryClient.setQueryData(
        THREADS_QUERY_KEYS.DETAIL(threadId),
        updatedThread
      );

      // Update specific thread trong threads list cache thay vì invalidate toàn bộ
      const threadsQueries = queryClient.getQueriesData({ queryKey: THREADS_QUERY_KEYS.ALL });
      threadsQueries.forEach(([queryKey, data]) => {
        if (data && typeof data === 'object' && 'items' in data) {
          const threadsData = data as GetThreadsResponse;
          if (threadsData.items) {
            const updatedItems = threadsData.items.map((thread: ThreadItem) =>
              thread.threadId === threadId
                ? { ...thread, name: updatedThread.name, updatedAt: updatedThread.updatedAt }
                : thread
            );
            queryClient.setQueryData(queryKey, {
              ...threadsData,
              items: updatedItems
            });
          }
        }
      });

      console.log('[useOptimisticUpdateThreadWithEvents] Updated thread in cache without invalidation:', {
        threadId,
        newName: updatedThread.name
      });

      // Emit event callback
      onThreadNameChanged?.(threadId, updatedThread.name);
    },
    onError: (error: Error) => {
      onError?.(error);
    }
  });

  const optimisticUpdate = async (threadId: string, newName: string) => {
    console.log('[useOptimisticUpdateThreadWithEvents] Starting optimistic update:', { threadId, newName });

    // Cancel any outgoing refetches
    await queryClient.cancelQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });
    await queryClient.cancelQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

    // Snapshot the previous values
    const previousThreadDetail = queryClient.getQueryData(THREADS_QUERY_KEYS.DETAIL(threadId));
    const previousThreadsList = queryClient.getQueriesData({ queryKey: THREADS_QUERY_KEYS.ALL });

    // Optimistically update thread detail
    if (previousThreadDetail) {
      queryClient.setQueryData(THREADS_QUERY_KEYS.DETAIL(threadId), {
        ...previousThreadDetail,
        name: newName,
        updatedAt: Date.now()
      });
    }

    // Optimistically update threads list
    previousThreadsList.forEach(([queryKey, data]) => {
      if (data && typeof data === 'object' && 'items' in data) {
        const threadsData = data as { items: ThreadDetailResponse[]; [key: string]: unknown };
        if (threadsData.items) {
          const updatedItems = threadsData.items.map((thread: ThreadDetailResponse) =>
            thread.threadId === threadId
              ? { ...thread, name: newName, updatedAt: Date.now() }
              : thread
          );
          queryClient.setQueryData(queryKey, {
            ...threadsData,
            items: updatedItems
          });
        }
      }
    });

    // Perform the mutation
    try {
      await updateMutation.mutateAsync({
        threadId,
        data: { name: newName }
      });
    } catch (error) {
      // Rollback on error
      if (previousThreadDetail) {
        queryClient.setQueryData(THREADS_QUERY_KEYS.DETAIL(threadId), previousThreadDetail);
      }
      previousThreadsList.forEach(([queryKey, data]) => {
        queryClient.setQueryData(queryKey, data);
      });
      throw error;
    }
  };

  return {
    optimisticUpdate,
    isLoading: updateMutation.isPending,
    error: updateMutation.error,
  };
};
