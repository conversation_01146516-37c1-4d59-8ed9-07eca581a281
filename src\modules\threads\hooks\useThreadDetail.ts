/**
 * React Query hook cho Thread detail operations
 */

import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadsService } from '../services';
import { ThreadDetailResponse } from '@/shared/types/chat-streaming.types';
import { isThreadDeleted } from '../services/deleted-thread-tracker.service';

/**
 * Hook để lấy chi tiết thread với enhanced error handling
 */
export const useThreadDetail = (
  threadId: string | undefined,
  options?: UseQueryOptions<ThreadDetailResponse>
) => {
  return useQuery({
    queryKey: THREADS_QUERY_KEYS.DETAIL(threadId!),
    queryFn: async () => {
      try {
        console.log('[useThreadDetail] Fetching thread detail:', threadId);
        return await ThreadsService.getThreadDetail(threadId!);
      } catch (error) {
        // Enhanced error handling cho deleted threads
        if (error instanceof Error && (error.message.includes('404') || error.message.includes('Not Found'))) {
          console.warn('[useThreadDetail] Thread not found (possibly deleted):', threadId);
          throw new Error(`Thread ${threadId} not found (possibly deleted)`);
        }
        throw error;
      }
    },
    enabled: !!threadId && !isThreadDeleted(threadId), // Chỉ chạy khi có threadId và thread chưa bị xóa
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Không retry nếu là 404 error (thread đã bị xóa)
      if (error instanceof Error && (error.message.includes('404') || error.message.includes('Not Found'))) {
        return false;
      }
      // Retry tối đa 2 lần cho các lỗi khác
      return failureCount < 2;
    },
    ...options,
  });
};
