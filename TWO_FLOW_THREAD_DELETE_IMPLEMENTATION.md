# Two-Flow Thread Delete Implementation

## Tổng quan

Đ<PERSON> implement 2 luồng xử lý xóa thread khác nhau dựa trên trạng thái `isActive` của thread trong `ThreadCard`:

## 🔄 **Luồng 1: Thread không active (isActive = false)**

### Đặc điểm:
- ✅ X<PERSON>a bình thường, không cần integration
- ✅ Chỉ gọi API xóa và update UI
- ✅ Không ảnh hưởng đến chat panel
- ✅ Không cần xử lý Redux state

### Implementation:

#### Hook: `useSimpleThreadDelete`
```typescript
// Chỉ xử lý API call và cache management
const deleteThreadMutation = useMutation({
  mutationFn: async (threadId: string) => {
    await ThreadsService.deleteThread(threadId);
    return { deletedThreadId: threadId };
  },
  onSuccess: ({ deletedThreadId }) => {
    // Remove từ cache
    queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });
    
    // Chỉ invalidate list queries
    queryClient.invalidateQueries({ 
      queryKey: THREADS_QUERY_KEYS.ALL,
      predicate: (query) => query.queryKey.includes('list') || query.queryKey.includes('paginated')
    });
    
    // Trigger callback
    onThreadDeleted?.(deletedThreadId);
  }
});
```

#### Luồng hoạt động:
1. User click delete → Confirm modal
2. Call `ThreadsService.deleteThread(threadId)`
3. Remove detail query từ cache
4. Invalidate list queries
5. Update UI (thread biến mất khỏi list)
6. Trigger `onThreadDeleted` callback

---

## 🔄 **Luồng 2: Thread active (isActive = true)**

### Đặc điểm:
- ✅ Cần xử lý integration với chat panel
- ✅ Tìm thread tiếp theo để switch
- ✅ Đồng bộ với Redux state
- ✅ Auto-create thread nếu không còn thread nào
- ✅ Sync với ChatPanel qua Redux

### Implementation:

#### Hook: `useActiveThreadDelete`
```typescript
const deleteActiveThreadMutation = useMutation({
  mutationFn: async (threadId: string) => {
    // 1. Delete API
    await ThreadsService.deleteThread(threadId);
    
    // 2. Find next thread hoặc create new
    const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);
    
    if (remainingThreads.length > 0) {
      // Switch to existing thread
      const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
      nextThreadId = sortedThreads[0]?.threadId;
      await externalChatStream.switchToThread(nextThreadId);
    } else {
      // Create new thread
      const newThread = await externalChatStream.createNewThread('New Chat');
      nextThreadId = newThread.threadId;
      isNewThreadCreated = true;
    }
    
    return { deletedThreadId: threadId, nextThreadId, isNewThreadCreated };
  },
  onSuccess: ({ deletedThreadId, nextThreadId, isNewThreadCreated }) => {
    // Update Redux state IMMEDIATELY
    dispatch(setCurrentThread({
      threadId: nextThreadId,
      threadName: `Thread ${nextThreadId.slice(-8)}`,
      isNew: isNewThreadCreated || false
    }));
    
    // Dispatch thread deleted event
    dispatch(threadDeleted({
      threadId: deletedThreadId,
      nextThreadId: nextThreadId
    }));
    
    // Cache management
    queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });
    queryClient.invalidateQueries({ /* selective invalidation */ });
  }
});
```

#### Luồng hoạt động:
1. User click delete → Confirm modal
2. Call `ThreadsService.deleteThread(threadId)`
3. Tìm thread tiếp theo:
   - Nếu có threads khác → Switch to latest thread
   - Nếu không có → Create new thread
4. Update Redux state immediately
5. Dispatch `threadDeleted` event
6. ChatPanel listen Redux changes và sync UI
7. Cache management và UI update

---

## 🏗️ **Architecture Changes**

### 1. ThreadCard Component
```typescript
// Thêm callback cho active thread delete
interface ThreadCardProps {
  onDelete?: (threadId: string) => void;           // Luồng 1
  onDeleteActive?: (threadId: string) => void;     // Luồng 2
}

// Logic phân luồng
const handleConfirmDelete = () => {
  if (isActive) {
    onDeleteActive?.(thread.threadId);  // Luồng 2
  } else {
    onDelete?.(thread.threadId);        // Luồng 1
  }
};
```

### 2. ThreadsGrid Component
```typescript
// Sử dụng 2 hook riêng biệt
const simpleDeleteHook = useSimpleThreadDelete({
  onThreadDeleted: (threadId) => onThreadDeleted?.(threadId)
});

const activeDeleteHook = useActiveThreadDelete({
  availableThreads: threads,
  externalChatStream: externalChatStream,
  onThreadDeleted: (deletedThreadId, nextThreadId) => onThreadDeleted?.(deletedThreadId)
});

// Truyền callbacks riêng biệt
<ThreadCard
  onDelete={simpleDeleteHook.deleteThread}
  onDeleteActive={activeDeleteHook.deleteActiveThread}
  isDeleting={
    simpleDeleteHook.isThreadDeleting(thread.threadId) || 
    activeDeleteHook.isThreadDeleting(thread.threadId)
  }
/>
```

### 3. ThreadsPage Integration
```typescript
// Truyền externalChatStream cho ThreadsGrid
<ThreadsGrid
  externalChatStream={enableChatIntegration && chatConfig ? {
    switchToThread: chatStream.switchToThread,
    createNewThread: chatStream.createNewThread
  } : undefined}
/>
```

---

## 🔄 **Redux Integration**

### Thread Integration Slice
```typescript
// Actions được dispatch từ active delete hook
dispatch(setCurrentThread({
  threadId: nextThreadId,
  threadName: fallbackName,
  isNew: isNewThreadCreated
}));

dispatch(threadDeleted({
  threadId: deletedThreadId,
  nextThreadId: nextThreadId
}));
```

### ChatPanel Sync
- ChatPanel listen Redux state changes
- Auto-update UI khi có thread switch
- Maintain bidirectional sync

---

## ✅ **Benefits**

### 1. Clear Separation of Concerns
- Simple threads → Simple logic
- Active threads → Complex integration logic

### 2. Performance Optimization
- Không cần integration overhead cho non-active threads
- Selective cache invalidation

### 3. Maintainability
- 2 hook riêng biệt, dễ debug và maintain
- Clear interfaces và responsibilities

### 4. User Experience
- Non-active threads: Instant delete
- Active threads: Seamless transition to next thread
- No "stuck" states

---

## 🧪 **Testing Scenarios**

### Luồng 1 (Non-active):
1. Tạo multiple threads
2. Xóa thread không active
3. Verify: Thread biến mất, không ảnh hưởng chat panel

### Luồng 2 (Active):
1. Tạo multiple threads, active 1 thread
2. Xóa active thread
3. Verify: Auto-switch to next thread, chat panel sync

### Edge Cases:
1. Xóa thread cuối cùng (active) → Auto-create new thread
2. Switch thread thất bại → Graceful error handling
3. Network errors → Proper error states

---

## 📁 **Files Modified**

1. `ThreadCard.tsx` - Phân luồng logic
2. `useSimpleThreadDelete.ts` - Hook luồng 1
3. `useActiveThreadDelete.ts` - Hook luồng 2  
4. `ThreadsGrid.tsx` - Integration 2 hooks
5. `ThreadsPage.tsx` - Truyền externalChatStream

Tất cả logic cũ đã được remove để tránh conflicts và confusion.
