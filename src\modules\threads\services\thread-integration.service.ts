/**
 * Thread Integration Service
 * Service để handle thread integration events và React Query cache management
 */

import { useQueryClient } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadEventCallbacks } from '@/shared/hooks/common/useChatStream';

/**
 * Thread Integration Service Class
 */
export class ThreadIntegrationService {
  private queryClient: any;
  private eventCallbacks: ThreadEventCallbacks = {};

  constructor(queryClient: any) {
    this.queryClient = queryClient;
  }

  /**
   * Set event callbacks
   */
  setEventCallbacks(callbacks: ThreadEventCallbacks) {
    this.eventCallbacks = { ...this.eventCallbacks, ...callbacks };
  }

  /**
   * Handle thread created event
   */
  handleThreadCreated = (threadId: string, threadName: string) => {
    console.log('[ThreadIntegrationService] Thread created:', { threadId, threadName });

    // Invalidate all threads queries để refresh danh sách
    this.queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

    // Call external callback if provided
    this.eventCallbacks.onThreadCreated?.(threadId, threadName);
  };

  /**
   * Handle thread loaded event
   */
  handleThreadLoaded = (threadId: string, threadName: string) => {
    console.log('[ThreadIntegrationService] Thread loaded:', { threadId, threadName });

    // Call external callback if provided
    this.eventCallbacks.onThreadLoaded?.(threadId, threadName);
  };

  /**
   * Handle thread switched event
   */
  handleThreadSwitched = (fromThreadId: string, toThreadId: string) => {
    console.log('[ThreadIntegrationService] Thread switched:', { fromThreadId, toThreadId });

    // Call external callback if provided
    this.eventCallbacks.onThreadSwitched?.(fromThreadId, toThreadId);
  };

  /**
   * Handle thread name changed event
   */
  handleThreadNameChanged = (threadId: string, newName: string) => {
    console.log('[ThreadIntegrationService] Thread name changed:', { threadId, newName });

    // Invalidate threads queries để refresh tên thread
    this.queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

    // Update specific thread detail cache if exists
    this.queryClient.setQueryData(
      THREADS_QUERY_KEYS.DETAIL(threadId),
      (oldData: any) => {
        if (oldData) {
          return { ...oldData, name: newName };
        }
        return oldData;
      }
    );

    // Call external callback if provided
    this.eventCallbacks.onThreadNameChanged?.(threadId, newName);
  };

  /**
   * Handle thread deleted event với auto-switch logic
   */
  handleThreadDeleted = async (threadId: string, availableThreads?: any[]) => {
    console.log('[ThreadIntegrationService] Thread deleted:', { threadId });

    // Remove thread detail from cache để tránh 404 error
    this.queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });

    // Chỉ invalidate list queries, KHÔNG invalidate detail queries
    this.queryClient.invalidateQueries({
      queryKey: THREADS_QUERY_KEYS.ALL,
      predicate: (query: any) => {
        return query.queryKey.includes('list') || query.queryKey.includes('paginated');
      }
    });

    // Find next thread để auto-switch nếu cần
    let nextThreadId: string | null = null;
    if (availableThreads && availableThreads.length > 0) {
      const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);
      if (remainingThreads.length > 0) {
        // Sort by updatedAt descending để lấy thread mới nhất
        const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
        nextThreadId = sortedThreads[0].threadId;
        console.log('[ThreadIntegrationService] Found next thread for auto-switch:', nextThreadId);
      }
    }

    // Call external callback với nextThreadId
    this.eventCallbacks.onThreadDeleted?.(threadId);

    return nextThreadId;
  };

  /**
   * Get thread event callbacks for useChatStream
   */
  getThreadEventCallbacks(): ThreadEventCallbacks {
    return {
      onThreadCreated: this.handleThreadCreated,
      onThreadLoaded: this.handleThreadLoaded,
      onThreadSwitched: this.handleThreadSwitched,
      onThreadNameChanged: this.handleThreadNameChanged,
      onThreadDeleted: this.handleThreadDeleted,
    };
  }
}

/**
 * Hook để sử dụng ThreadIntegrationService
 */
export const useThreadIntegrationService = (callbacks?: ThreadEventCallbacks) => {
  const queryClient = useQueryClient();

  // Create service instance
  const service = new ThreadIntegrationService(queryClient);

  // Set callbacks if provided
  if (callbacks) {
    service.setEventCallbacks(callbacks);
  }

  return service;
};
