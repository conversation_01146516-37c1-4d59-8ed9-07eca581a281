/**
 * Thread Integration Redux Slice
 * Quản lý state cho thread integration giữa ChatPanel và ThreadsPage
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ThreadInfo {
  threadId: string;
  name: string;
  isNew?: boolean; // Flag để indicate thread mới tạo
}

interface ThreadIntegrationState {
  currentThreadId: string | null;
  currentThreadName: string | null;
  currentThreadInfo: ThreadInfo | null;
  isThreadSwitching: boolean;
  lastUpdated: number;
  // Events để trigger UI updates
  events: {
    threadCreated: ThreadInfo | null;
    threadUpdated: ThreadInfo | null;
    threadDeleted: string | null;
    datasetConversationCreated: ThreadInfo | null; // Event cho dataset conversation
  };
}

const initialState: ThreadIntegrationState = {
  currentThreadId: null,
  currentThreadName: null,
  currentThreadInfo: null,
  isThreadSwitching: false,
  lastUpdated: 0,
  events: {
    threadCreated: null,
    threadUpdated: null,
    threadDeleted: null,
    datasetConversationCreated: null
  }
};

const threadIntegrationSlice = createSlice({
  name: 'threadIntegration',
  initialState,
  reducers: {
    setCurrentThread: (state, action: PayloadAction<{
      threadId: string | null;
      threadName?: string | null;
      isNew?: boolean;
    }>) => {
      const { threadId, threadName, isNew } = action.payload;
      state.currentThreadId = threadId;
      state.currentThreadName = threadName || null;

      // Update full thread info if provided
      if (threadId) {
        state.currentThreadInfo = {
          threadId,
          name: threadName || '',
          isNew: isNew || false
        };
      } else {
        state.currentThreadInfo = null;
      }

      state.isThreadSwitching = false;
      state.lastUpdated = Date.now();
    },

    updateThreadName: (state, action: PayloadAction<{ threadId: string; newName: string }>) => {
      const { threadId, newName } = action.payload;
      // Update current thread name if this is the current thread
      if (state.currentThreadId === threadId) {
        state.currentThreadName = newName;
        if (state.currentThreadInfo) {
          state.currentThreadInfo.name = newName;
        }
      }

      // Set update event để ThreadsPage có thể listen
      state.events.threadUpdated = {
        threadId,
        name: newName,
        isNew: false
      };

      state.lastUpdated = Date.now();
    },

    setThreadSwitching: (state, action: PayloadAction<boolean>) => {
      state.isThreadSwitching = action.payload;
      state.lastUpdated = Date.now();
    },

    clearCurrentThread: (state) => {
      state.currentThreadId = null;
      state.currentThreadName = null;
      state.currentThreadInfo = null;
      state.isThreadSwitching = false;
      state.lastUpdated = Date.now();
    },

    // Action để trigger thread events
    threadCreated: (state, action: PayloadAction<{
      threadId: string;
      threadName: string;
    }>) => {
      const { threadId, threadName } = action.payload;
      const now = Date.now();

      // Set as current thread
      state.currentThreadId = threadId;
      state.currentThreadName = threadName;
      state.currentThreadInfo = {
        threadId,
        name: threadName,
        isNew: true
      };

      // Set creation event để ThreadsPage có thể listen
      state.events.threadCreated = {
        threadId,
        name: threadName,
        isNew: true
      };

      state.isThreadSwitching = false;
      state.lastUpdated = now;
    },

    threadSwitched: (state, action: PayloadAction<{ fromId: string; toId: string }>) => {
      const { toId } = action.payload;
      state.currentThreadId = toId;
      state.isThreadSwitching = true; // Will be cleared when thread loads
      state.lastUpdated = Date.now();
    },

    threadDeleted: (state, action: PayloadAction<{ threadId: string; nextThreadId?: string | null }>) => {
      const { threadId, nextThreadId } = action.payload;

      console.log('[threadIntegrationSlice] Thread deleted:', {
        threadId,
        nextThreadId,
        currentThreadId: state.currentThreadId,
        isCurrentThread: state.currentThreadId === threadId
      });

      // Nếu thread bị xóa là current thread
      if (state.currentThreadId === threadId) {
        if (nextThreadId) {
          // Auto-switch sang thread tiếp theo
          console.log('[threadIntegrationSlice] Auto-switching to next thread:', nextThreadId);
          state.currentThreadId = nextThreadId;
          state.currentThreadName = null; // Sẽ được update khi thread load
          state.currentThreadInfo = {
            threadId: nextThreadId,
            name: `${nextThreadId.slice(-8)}`, // Fallback name
            isNew: false
          };
          state.isThreadSwitching = true; // Sẽ được clear khi thread load xong
        } else {
          // Không có thread nào khác, clear current thread
          console.log('[threadIntegrationSlice] No next thread, clearing current thread');
          state.currentThreadId = null;
          state.currentThreadName = null;
          state.currentThreadInfo = null;
          state.isThreadSwitching = false;
        }
      }

      // Set deletion event để ThreadsPage có thể listen
      state.events.threadDeleted = threadId;

      state.lastUpdated = Date.now();
    },

    // Action để clear events sau khi đã được processed
    clearEvents: (state) => {
      state.events = {
        threadCreated: null,
        threadUpdated: null,
        threadDeleted: null,
        datasetConversationCreated: null
      };
    },

    // Action để handle dataset conversation creation
    datasetConversationCreated: (state, action: PayloadAction<{
      threadId: string;
      threadName: string;
    }>) => {
      const { threadId, threadName } = action.payload;

      // Set dataset conversation creation event để ThreadsPage có thể listen
      state.events.datasetConversationCreated = {
        threadId,
        name: threadName,
        isNew: true
      };

      state.lastUpdated = Date.now();
    }
  }
});

export const {
  setCurrentThread,
  updateThreadName,
  setThreadSwitching,
  clearCurrentThread,
  threadCreated,
  threadSwitched,
  threadDeleted,
  clearEvents,
  datasetConversationCreated
} = threadIntegrationSlice.actions;

export default threadIntegrationSlice.reducer;

// Selectors
export const selectCurrentThread = (state: { threadIntegration: ThreadIntegrationState }) => ({
  threadId: state.threadIntegration.currentThreadId,
  threadName: state.threadIntegration.currentThreadName,
  isThreadSwitching: state.threadIntegration.isThreadSwitching,
  lastUpdated: state.threadIntegration.lastUpdated
});

export const selectCurrentThreadId = (state: { threadIntegration: ThreadIntegrationState }) => 
  state.threadIntegration.currentThreadId;

export const selectCurrentThreadName = (state: { threadIntegration: ThreadIntegrationState }) => 
  state.threadIntegration.currentThreadName;

export const selectIsThreadSwitching = (state: { threadIntegration: ThreadIntegrationState }) =>
  state.threadIntegration.isThreadSwitching;

export const selectCurrentThreadInfo = (state: { threadIntegration: ThreadIntegrationState }) =>
  state.threadIntegration.currentThreadInfo;

export const selectThreadEvents = (state: { threadIntegration: ThreadIntegrationState }) =>
  state.threadIntegration.events;
