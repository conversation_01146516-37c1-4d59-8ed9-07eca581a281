/**
 * Voice Configuration Service
 * Quản lý cấu hình cho Speech-to-Text và Text-to-Speech services
 */

import { SpeechToTextConfig } from './speech-to-text.service';
import { TextToSpeechConfig } from './text-to-speech.service';

/**
 * Voice chat configuration
 */
export interface VoiceChatConfig {
  enabled: boolean;
  speechToText: SpeechToTextConfig;
  textToSpeech: TextToSpeechConfig;
  autoPlay: boolean;
  autoStop: boolean;
  maxRecordingDuration: number; // seconds
  silenceTimeout: number; // seconds
}

/**
 * Supported languages
 */
export const SUPPORTED_LANGUAGES = {
  'vi-VN': {
    name: 'Tiếng Việt',
    speechCode: 'vi-VN',
    ttsVoices: [
      { name: 'vi-VN-Standard-A', gender: 'FEMALE', description: 'Giọng nữ chuẩn' },
      { name: 'vi-VN-Standard-B', gender: 'MALE', description: 'Giọng nam chuẩn' },
      { name: 'vi-VN-Standard-C', gender: 'FEMALE', description: 'Giọng nữ tự nhiên' },
      { name: 'vi-VN-Standard-D', gender: 'MALE', description: 'Giọng nam tự nhiên' },
    ],
  },
  'en-US': {
    name: 'English (US)',
    speechCode: 'en-US',
    ttsVoices: [
      { name: 'en-US-Standard-A', gender: 'MALE', description: 'Standard Male' },
      { name: 'en-US-Standard-B', gender: 'MALE', description: 'Standard Male' },
      { name: 'en-US-Standard-C', gender: 'FEMALE', description: 'Standard Female' },
      { name: 'en-US-Standard-D', gender: 'MALE', description: 'Standard Male' },
      { name: 'en-US-Standard-E', gender: 'FEMALE', description: 'Standard Female' },
    ],
  },
} as const;

/**
 * Default voice configuration
 */
const DEFAULT_CONFIG: VoiceChatConfig = {
  enabled: false,
  speechToText: {
    projectId: '',
    apiKey: '',
    languageCode: 'vi-VN',
    sampleRateHertz: 16000,
    encoding: 'WEBM_OPUS',
    enableAutomaticPunctuation: true,
    enableWordTimeOffsets: false,
    model: 'latest_long',
  },
  textToSpeech: {
    projectId: '',
    apiKey: '',
    languageCode: 'vi-VN',
    voiceName: 'vi-VN-Standard-A',
    ssmlGender: 'FEMALE',
    audioEncoding: 'MP3',
    speakingRate: 1.0,
    pitch: 0.0,
    volumeGainDb: 0.0,
  },
  autoPlay: true,
  autoStop: true,
  maxRecordingDuration: 60, // 1 minute
  silenceTimeout: 3, // 3 seconds
};

/**
 * Voice Configuration Service Class
 */
export class VoiceConfigService {
  private config: VoiceChatConfig;
  private readonly STORAGE_KEY = 'voice-chat-config';

  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * Load configuration từ localStorage và environment variables
   */
  private loadConfig(): VoiceChatConfig {
    try {
      // Load từ localStorage
      const savedConfig = localStorage.getItem(this.STORAGE_KEY);
      let config = savedConfig ? JSON.parse(savedConfig) : {};

      // Merge với environment variables
      const envConfig = this.getEnvironmentConfig();
      config = { ...DEFAULT_CONFIG, ...config, ...envConfig };

      console.log('[VoiceConfig] Configuration loaded:', config);
      return config;

    } catch (error) {
      console.error('[VoiceConfig] Failed to load config, using defaults:', error);
      return { ...DEFAULT_CONFIG, ...this.getEnvironmentConfig() };
    }
  }

  /**
   * Lấy configuration từ environment variables
   */
  private getEnvironmentConfig(): Partial<VoiceChatConfig> {
    const projectId = import.meta.env.VITE_GOOGLE_CLOUD_PROJECT_ID;
    const apiKey = import.meta.env.VITE_GOOGLE_CLOUD_API_KEY;
    const enabled = import.meta.env.VITE_VOICE_CHAT_ENABLED === 'true';
    const speechLanguage = import.meta.env.VITE_SPEECH_LANGUAGE || 'vi-VN';
    const ttsVoiceName = import.meta.env.VITE_TTS_VOICE_NAME || 'vi-VN-Standard-A';

    const envConfig: Partial<VoiceChatConfig> = {
      enabled,
    };

    if (projectId && apiKey) {
      envConfig.speechToText = {
        projectId,
        apiKey,
        languageCode: speechLanguage,
        sampleRateHertz: 16000,
        encoding: 'WEBM_OPUS',
        enableAutomaticPunctuation: true,
        enableWordTimeOffsets: false,
        model: 'latest_long',
      };

      envConfig.textToSpeech = {
        projectId,
        apiKey,
        languageCode: speechLanguage,
        voiceName: ttsVoiceName,
        ssmlGender: 'FEMALE',
        audioEncoding: 'MP3',
        speakingRate: 1.0,
        pitch: 0.0,
        volumeGainDb: 0.0,
      };
    }

    return envConfig;
  }

  /**
   * Lấy toàn bộ configuration
   */
  getConfig(): VoiceChatConfig {
    return { ...this.config };
  }

  /**
   * Lấy Speech-to-Text configuration
   */
  getSpeechToTextConfig(): SpeechToTextConfig {
    return { ...this.config.speechToText };
  }

  /**
   * Lấy Text-to-Speech configuration
   */
  getTextToSpeechConfig(): TextToSpeechConfig {
    return { ...this.config.textToSpeech };
  }

  /**
   * Cập nhật configuration
   */
  updateConfig(updates: Partial<VoiceChatConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
    console.log('[VoiceConfig] Configuration updated:', this.config);
  }

  /**
   * Cập nhật Speech-to-Text configuration
   */
  updateSpeechToTextConfig(updates: Partial<SpeechToTextConfig>): void {
    this.config.speechToText = { ...this.config.speechToText, ...updates };
    this.saveConfig();
    console.log('[VoiceConfig] Speech-to-Text config updated');
  }

  /**
   * Cập nhật Text-to-Speech configuration
   */
  updateTextToSpeechConfig(updates: Partial<TextToSpeechConfig>): void {
    this.config.textToSpeech = { ...this.config.textToSpeech, ...updates };
    this.saveConfig();
    console.log('[VoiceConfig] Text-to-Speech config updated');
  }

  /**
   * Lưu configuration vào localStorage
   */
  private saveConfig(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.error('[VoiceConfig] Failed to save config:', error);
    }
  }

  /**
   * Reset về configuration mặc định
   */
  resetToDefaults(): void {
    this.config = { ...DEFAULT_CONFIG, ...this.getEnvironmentConfig() };
    this.saveConfig();
    console.log('[VoiceConfig] Configuration reset to defaults');
  }

  /**
   * Kiểm tra xem voice chat có được enable không
   */
  isEnabled(): boolean {
    return this.config.enabled && this.isConfigured();
  }

  /**
   * Kiểm tra xem configuration có đầy đủ không
   */
  isConfigured(): boolean {
    const sttConfig = this.config.speechToText;
    const ttsConfig = this.config.textToSpeech;

    return !!(
      sttConfig.projectId &&
      sttConfig.apiKey &&
      ttsConfig.projectId &&
      ttsConfig.apiKey
    );
  }

  /**
   * Lấy danh sách ngôn ngữ được hỗ trợ
   */
  getSupportedLanguages(): typeof SUPPORTED_LANGUAGES {
    return SUPPORTED_LANGUAGES;
  }

  /**
   * Lấy danh sách voices cho một ngôn ngữ
   */
  getVoicesForLanguage(languageCode: keyof typeof SUPPORTED_LANGUAGES) {
    return SUPPORTED_LANGUAGES[languageCode]?.ttsVoices || [];
  }

  /**
   * Validate configuration
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.config.speechToText.projectId) {
      errors.push('Missing Google Cloud Project ID for Speech-to-Text');
    }

    if (!this.config.speechToText.apiKey) {
      errors.push('Missing API Key for Speech-to-Text');
    }

    if (!this.config.textToSpeech.projectId) {
      errors.push('Missing Google Cloud Project ID for Text-to-Speech');
    }

    if (!this.config.textToSpeech.apiKey) {
      errors.push('Missing API Key for Text-to-Speech');
    }

    if (this.config.maxRecordingDuration <= 0) {
      errors.push('Max recording duration must be greater than 0');
    }

    if (this.config.silenceTimeout <= 0) {
      errors.push('Silence timeout must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Export configuration
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration
   */
  importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson);
      this.config = { ...DEFAULT_CONFIG, ...importedConfig };
      this.saveConfig();
      console.log('[VoiceConfig] Configuration imported successfully');
    } catch (error) {
      console.error('[VoiceConfig] Failed to import config:', error);
      throw new Error('Invalid configuration format');
    }
  }
}

/**
 * Singleton instance
 */
let voiceConfigService: VoiceConfigService | null = null;

/**
 * Factory function để lấy singleton instance
 */
export function getVoiceConfigService(): VoiceConfigService {
  if (!voiceConfigService) {
    voiceConfigService = new VoiceConfigService();
  }
  return voiceConfigService;
}

/**
 * Default export
 */
export default VoiceConfigService;
