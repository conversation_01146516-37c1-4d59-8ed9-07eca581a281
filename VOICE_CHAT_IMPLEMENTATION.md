# Voice Chat Implementation Guide

## Tổng quan

Tài liệu này mô tả việc tích hợp Google Cloud Speech-to-Text và Text-to-Speech vào ứng dụng chat để tạo ra tính năng voice chat hoàn chỉnh.

## Kiến trúc

### 1. Services Layer

#### SpeechToTextService (`src/shared/services/speech-to-text.service.ts`)
- Quản lý Google Cloud Speech-to-Text API
- Streaming recognition với microphone input
- Real-time transcription với confidence scores
- Error handling và retry logic

#### TextToSpeechService (`src/shared/services/text-to-speech.service.ts`)
- Quản lý Google Cloud Text-to-Speech API
- Queue-based audio synthesis và playback
- Multiple voice options và audio formats
- Audio queue management

#### VoiceConfigService (`src/shared/services/voice-config.service.ts`)
- Centralized configuration management
- Environment variables integration
- LocalStorage persistence
- Validation và error checking

### 2. Hooks Layer

#### useSpeechToText (`src/shared/hooks/common/useSpeechToText.ts`)
- React hook cho Speech-to-Text functionality
- Microphone permission handling
- Real-time transcription state management
- Auto-stop và silence detection

#### useTextToSpeech (`src/shared/hooks/common/useTextToSpeech.ts`)
- React hook cho Text-to-Speech functionality
- Audio synthesis và playback control
- Queue management
- Playback state tracking

#### useVoiceChat (`src/shared/hooks/common/useVoiceChat.ts`)
- Unified hook tích hợp cả STT và TTS
- Voice chat modes: input, output, both, disabled
- Supervisor message collection cho voice responses
- Error handling và notifications

### 3. Components Layer

#### ChatInputBox (Updated)
- Tích hợp voice input button
- Real-time voice transcript display
- Voice recording indicators
- Voice chat mode support

#### VoiceChatDemo (`src/shared/components/demo/VoiceChatDemo.tsx`)
- Demo component để test voice functionality
- Voice mode controls
- Message history display
- Test voice output functions

## Cấu hình

### 1. Environment Variables

Thêm vào `.env` file:

```env
# Google Cloud Configuration for Voice Chat
VITE_GOOGLE_CLOUD_PROJECT_ID=your-project-id
VITE_GOOGLE_CLOUD_API_KEY=your-api-key

# Voice Chat Settings
VITE_VOICE_CHAT_ENABLED=true
VITE_SPEECH_LANGUAGE=vi-VN
VITE_TTS_VOICE_NAME=vi-VN-Standard-A
```

### 2. Google Cloud Setup

1. Tạo Google Cloud Project
2. Enable Speech-to-Text API và Text-to-Speech API
3. Tạo API Key hoặc Service Account
4. Cấu hình CORS cho browser requests

## Sử dụng

### 1. Basic Voice Chat

```typescript
import { useVoiceChat } from '@/shared/hooks/common';

const MyComponent = () => {
  const voiceChat = useVoiceChat({
    mode: 'both',
    onVoiceInput: (transcript, isFinal) => {
      if (isFinal) {
        console.log('Final transcript:', transcript);
      }
    },
    onVoiceOutput: (text) => {
      console.log('Speaking:', text);
    },
  });

  return (
    <div>
      <button onClick={voiceChat.toggleRecording}>
        {voiceChat.state.isRecording ? 'Stop' : 'Start'} Recording
      </button>
      
      <button onClick={() => voiceChat.speak('Hello World')}>
        Speak Text
      </button>
      
      <p>Transcript: {voiceChat.state.currentTranscript}</p>
    </div>
  );
};
```

### 2. ChatInputBox với Voice Chat

```typescript
import ChatInputBox from '@/shared/components/layout/chat-panel/ChatInputBox';

const ChatPanel = () => {
  const voiceChatRef = useRef(null);

  const handleVoiceTranscript = (transcript, isFinal) => {
    if (isFinal) {
      // Process final transcript
      sendMessage(transcript);
    }
  };

  const handleAIResponse = (responseText) => {
    // Speak AI response
    if (voiceChatRef.current) {
      voiceChatRef.current.speakText(responseText);
    }
  };

  return (
    <ChatInputBox
      enableVoiceChat={true}
      voiceChatMode="both"
      onVoiceTranscript={handleVoiceTranscript}
      voiceChatRef={voiceChatRef}
      // ... other props
    />
  );
};
```

### 3. Voice Response từ Chat Streaming

```typescript
// Trong component chat chính
useEffect(() => {
  // Listen to SSE events
  const handleSSEEvent = (event) => {
    if (event.type === 'stream_text_token' && event.data.role === 'supervisor') {
      // Collect supervisor tokens
      supervisorBuffer.current += event.data.text;
    }
    
    if (event.type === 'llm_stream_end' && event.data.role === 'supervisor') {
      // Stream ended, speak the complete response
      if (voiceChatRef.current && supervisorBuffer.current) {
        voiceChatRef.current.speakText(supervisorBuffer.current);
        supervisorBuffer.current = '';
      }
    }
  };

  // Setup SSE listener
  sseService.addEventListener('message', handleSSEEvent);
  
  return () => {
    sseService.removeEventListener('message', handleSSEEvent);
  };
}, []);
```

## Voice Chat Modes

### 1. Input Only (`mode: 'input'`)
- Chỉ cho phép voice input (Speech-to-Text)
- Text-to-Speech bị disable

### 2. Output Only (`mode: 'output'`)
- Chỉ cho phép voice output (Text-to-Speech)
- Speech-to-Text bị disable

### 3. Both (`mode: 'both'`)
- Cho phép cả voice input và output
- Full voice chat experience

### 4. Disabled (`mode: 'disabled'`)
- Tắt hoàn toàn voice chat functionality

## Error Handling

### 1. Permission Errors
- Microphone permission denied
- Auto-request permissions
- Fallback to text input

### 2. API Errors
- Google Cloud API failures
- Network connectivity issues
- Invalid credentials

### 3. Browser Compatibility
- MediaRecorder API support
- Audio API support
- Fallback mechanisms

## Performance Optimization

### 1. Audio Streaming
- Chunk-based audio processing
- Real-time transcription
- Memory management

### 2. Queue Management
- Audio synthesis queue
- Playback prioritization
- Resource cleanup

### 3. Network Efficiency
- Request batching
- Connection pooling
- Error retry logic

## Testing

### 1. Voice Chat Demo
- Truy cập VoiceChatDemo component
- Test các voice modes khác nhau
- Verify transcription accuracy

### 2. Integration Testing
- Test với chat streaming system
- Verify supervisor message collection
- Test voice response timing

### 3. Browser Testing
- Chrome, Firefox, Safari compatibility
- Mobile browser support
- Permission handling

## Troubleshooting

### 1. No Voice Input
- Check microphone permissions
- Verify Google Cloud credentials
- Check browser compatibility

### 2. No Voice Output
- Check audio playback permissions
- Verify TTS API configuration
- Check speaker/headphone connection

### 3. Poor Transcription Quality
- Check microphone quality
- Adjust language settings
- Verify network connection

## Future Enhancements

### 1. Advanced Features
- Voice commands recognition
- Multi-language support
- Custom voice models

### 2. UI/UX Improvements
- Voice level visualization
- Better recording indicators
- Voice settings panel

### 3. Performance
- Offline voice processing
- Edge computing integration
- Real-time optimization

## Dependencies

```json
{
  "@google-cloud/speech": "^7.1.0",
  "@google-cloud/text-to-speech": "^6.1.0"
}
```

## Browser Support

- Chrome 66+
- Firefox 60+
- Safari 14+
- Edge 79+

## Security Considerations

- API key protection
- Audio data privacy
- CORS configuration
- Rate limiting
