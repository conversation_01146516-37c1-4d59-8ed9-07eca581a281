/**
 * Speech-to-Text Service
 * Sử dụng Google Cloud Speech-to-Text API để chuyển đổi giọng nói thành text
 */

import { SpeechClient } from '@google-cloud/speech';

/**
 * Configuration cho Speech-to-Text
 */
export interface SpeechToTextConfig {
  projectId: string;
  apiKey: string;
  languageCode?: string;
  sampleRateHertz?: number;
  encoding?: string;
  enableAutomaticPunctuation?: boolean;
  enableWordTimeOffsets?: boolean;
  model?: string;
}

/**
 * Kết quả transcription
 */
export interface TranscriptionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  alternatives?: Array<{
    transcript: string;
    confidence: number;
  }>;
}

/**
 * Callback functions cho streaming recognition
 */
export interface SpeechRecognitionCallbacks {
  onResult?: (result: TranscriptionResult) => void;
  onError?: (error: Error) => void;
  onEnd?: () => void;
  onStart?: () => void;
}

/**
 * Speech-to-Text Service Class
 */
export class SpeechToTextService {
  private client: SpeechClient | null = null;
  private config: SpeechToTextConfig;
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private isRecording = false;
  private callbacks: SpeechRecognitionCallbacks = {};

  constructor(config: SpeechToTextConfig) {
    this.config = {
      languageCode: 'vi-VN',
      sampleRateHertz: 16000,
      encoding: 'WEBM_OPUS',
      enableAutomaticPunctuation: true,
      enableWordTimeOffsets: false,
      model: 'latest_long',
      ...config,
    };

    this.initializeClient();
  }

  /**
   * Khởi tạo Google Cloud Speech client
   */
  private initializeClient(): void {
    try {
      // Trong môi trường browser, chúng ta sẽ sử dụng REST API thay vì gRPC client
      // Client sẽ được khởi tạo khi cần thiết
      console.log('[SpeechToText] Service initialized with config:', this.config);
    } catch (error) {
      console.error('[SpeechToText] Failed to initialize client:', error);
      throw new Error('Failed to initialize Speech-to-Text client');
    }
  }

  /**
   * Bắt đầu recording và streaming recognition
   */
  async startRecording(callbacks: SpeechRecognitionCallbacks = {}): Promise<void> {
    if (this.isRecording) {
      throw new Error('Recording is already in progress');
    }

    this.callbacks = callbacks;

    try {
      // Yêu cầu quyền truy cập microphone
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRateHertz,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      // Tạo MediaRecorder để capture audio
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      // Setup event handlers
      this.setupMediaRecorderEvents();

      // Bắt đầu recording
      this.mediaRecorder.start(1000); // Collect data every 1 second
      this.isRecording = true;

      console.log('[SpeechToText] Recording started');
      this.callbacks.onStart?.();

    } catch (error) {
      console.error('[SpeechToText] Failed to start recording:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  /**
   * Dừng recording
   */
  async stopRecording(): Promise<void> {
    if (!this.isRecording) {
      return;
    }

    try {
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
      }

      if (this.audioStream) {
        this.audioStream.getTracks().forEach(track => track.stop());
        this.audioStream = null;
      }

      this.isRecording = false;
      console.log('[SpeechToText] Recording stopped');
      this.callbacks.onEnd?.();

    } catch (error) {
      console.error('[SpeechToText] Failed to stop recording:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Setup MediaRecorder event handlers
   */
  private setupMediaRecorderEvents(): void {
    if (!this.mediaRecorder) return;

    this.mediaRecorder.ondataavailable = async (event) => {
      if (event.data.size > 0) {
        try {
          await this.processAudioChunk(event.data);
        } catch (error) {
          console.error('[SpeechToText] Failed to process audio chunk:', error);
          this.callbacks.onError?.(error as Error);
        }
      }
    };

    this.mediaRecorder.onerror = (event) => {
      console.error('[SpeechToText] MediaRecorder error:', event);
      this.callbacks.onError?.(new Error('MediaRecorder error'));
    };
  }

  /**
   * Xử lý audio chunk và gửi đến Google Cloud Speech API
   */
  private async processAudioChunk(audioBlob: Blob): Promise<void> {
    try {
      // Convert blob to base64
      const arrayBuffer = await audioBlob.arrayBuffer();
      const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

      // Gọi Google Cloud Speech API qua REST
      const result = await this.callSpeechAPI(base64Audio);
      
      if (result && result.results && result.results.length > 0) {
        const recognition = result.results[0];
        const alternative = recognition.alternatives[0];

        const transcriptionResult: TranscriptionResult = {
          transcript: alternative.transcript || '',
          confidence: alternative.confidence || 0,
          isFinal: recognition.isFinal || false,
          alternatives: recognition.alternatives?.map(alt => ({
            transcript: alt.transcript || '',
            confidence: alt.confidence || 0,
          })),
        };

        this.callbacks.onResult?.(transcriptionResult);
      }

    } catch (error) {
      console.error('[SpeechToText] Failed to process audio chunk:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Gọi Google Cloud Speech API qua REST
   */
  private async callSpeechAPI(base64Audio: string): Promise<any> {
    const requestBody = {
      config: {
        encoding: this.config.encoding,
        sampleRateHertz: this.config.sampleRateHertz,
        languageCode: this.config.languageCode,
        enableAutomaticPunctuation: this.config.enableAutomaticPunctuation,
        model: this.config.model,
      },
      audio: {
        content: base64Audio,
      },
    };

    const response = await fetch(
      `https://speech.googleapis.com/v1/speech:recognize?key=${this.config.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      throw new Error(`Speech API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Kiểm tra trạng thái recording
   */
  get isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  /**
   * Cập nhật configuration
   */
  updateConfig(newConfig: Partial<SpeechToTextConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[SpeechToText] Config updated:', this.config);
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    if (this.isRecording) {
      this.stopRecording();
    }
    this.callbacks = {};
    console.log('[SpeechToText] Service disposed');
  }
}

/**
 * Factory function để tạo SpeechToTextService instance
 */
export function createSpeechToTextService(config: SpeechToTextConfig): SpeechToTextService {
  return new SpeechToTextService(config);
}

/**
 * Default export
 */
export default SpeechToTextService;
