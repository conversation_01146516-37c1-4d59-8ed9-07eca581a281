/**
 * Chat SSE Service
 * Quản lý kết nối Server-Sent Events cho chat streaming với Queue-Based Architecture
 */

import { StreamingController } from './streaming-controller.service';
import { TokenQueueItem } from './token-queue.service';

/**
 * Callback types cho SSE events
 */
export interface SSECallbacks {
  onConnected?: (data: unknown) => void;
  onTextToken?: (text: string, role: string) => void;
  onToolToken?: (role: string) => void;
  onToolCallStart?: (role: string) => void;
  onToolCallEnd?: (role: string) => void;
  onLLMStreamEnd?: (role: string) => void;
  onMessageCreated?: (messageId: string, threadId: string, role: string, contentPreview: string) => void;
  onStreamSessionEnd?: (reason: string) => void;
  onStreamEnd?: (threadId: string, runId: string) => void;
  onStreamError?: (error: string, details?: unknown) => void;
  onError?: (error: Error) => void;
  onClose?: () => void;
  onUpdateRPoint?: (rPointCost: number, updatedBalance: string, timestamp: number) => void;
}

/**
 * Enhanced SSE Callbacks với Queue Support
 */
export interface EnhancedSSECallbacks extends SSECallbacks {
  onTokenQueued?: (token: TokenQueueItem) => void;
  onQueueOverflow?: (droppedTokens: TokenQueueItem[]) => void;
  onStreamingModeChanged?: (useQueue: boolean) => void;
}

/**
 * Chat SSE Service Class với Queue-Based Streaming Support
 */
export class ChatSSEService {
  private eventSource: EventSource | null = null;
  private baseUrl: string;
  private debug: boolean;
  private callbacks: EnhancedSSECallbacks = {};
  private currentThreadId: string | null = null;
  private currentRunId: string | null = null;
  private isConnected: boolean = false;

  // Queue-based streaming
  private streamingController: StreamingController | null = null;
  private useQueueMode: boolean = true;
  private currentMessageId: string | null = null;
  private tokenIndex: number = 0;

  // Timeout management
  private timeoutTimer: NodeJS.Timeout | null = null;
  private lastEventTime: number = 0;
  private readonly TIMEOUT_DURATION = 60000; // 1 phút = 60,000ms
  private readonly HEARTBEAT_INTERVAL = 10000; // Check mỗi 10 giây

  constructor(baseUrl: string, debug: boolean = false) {
    this.baseUrl = baseUrl;
    this.debug = debug;
    this.initializeStreamingController();
  }

  /**
   * Initialize StreamingController cho queue-based streaming
   */
  private initializeStreamingController(): void {
    this.streamingController = new StreamingController({
      queueConfig: {
        maxSize: 1000,
        enableOverflowProtection: true,
        enableMetrics: true,
        debug: this.debug
      },
      processorConfig: {
        defaultSpeed: 50,
        bufferWaitTime: 500,
        maxWaitTime: 3000,
        enableAdaptiveSpeed: true,
        enableWordBoundaryDetection: true,
        debug: this.debug
      },
      processingInterval: 10,
      enableAutoProcessing: true,
      enableAdaptiveProcessing: true,
      debug: this.debug
    });

    // Setup streaming controller callbacks
    this.streamingController.setCallbacks({
      onQueueOverflow: (droppedTokens) => {
        this.callbacks.onQueueOverflow?.(droppedTokens);
      },
      onError: (error, context) => {
        this.log('StreamingController error', { error: error.message, context });
        this.callbacks.onError?.(error);
      }
    });
  }

  /**
   * Enable/disable queue mode
   */
  setQueueMode(enabled: boolean): void {
    this.useQueueMode = enabled;
    this.callbacks.onStreamingModeChanged?.(enabled);
    this.log('Queue mode changed', { enabled });
  }

  /**
   * Get StreamingController instance
   */
  getStreamingController(): StreamingController | null {
    return this.streamingController;
  }

  /**
   * Set current message ID cho streaming
   */
  setCurrentMessageId(messageId: string): void {
    this.currentMessageId = messageId;
    this.tokenIndex = 0;
    this.log('Current message ID set', { messageId });
  }

  /**
   * Log debug information
   */
  private log(message: string, data?: unknown): void {
    if (this.debug) {
      console.log(`[ChatSSEService] ${message}`, data || '');
    }
  }

  /**
   * Start timeout timer để disconnect nếu không nhận event trong 1 phút
   */
  private startTimeoutTimer(): void {
    this.clearTimeoutTimer();
    this.lastEventTime = Date.now();

    this.timeoutTimer = setInterval(() => {
      const now = Date.now();
      const timeSinceLastEvent = now - this.lastEventTime;

      // Chỉ trigger timeout nếu:
      // 1. Không có events trong 1 phút
      // 2. Connection vẫn đang active (isConnected = true)
      // 3. EventSource vẫn đang open
      if (timeSinceLastEvent >= this.TIMEOUT_DURATION &&
          this.isConnected &&
          this.eventSource?.readyState === EventSource.OPEN) {

        console.warn('[ChatSSEService] ⏰ SSE timeout: No events received for 1 minute, disconnecting...');
        this.log('SSE timeout triggered', {
          timeSinceLastEvent,
          timeoutDuration: this.TIMEOUT_DURATION,
          lastEventTime: new Date(this.lastEventTime).toISOString(),
          isConnected: this.isConnected,
          readyState: this.eventSource?.readyState
        });

        // Disconnect và notify error
        this.callbacks.onError?.(new Error('SSE connection timeout: No events received for 1 minute'));
        this.disconnect();
      } else {
        // Log heartbeat để debug - chỉ khi debug mode
        if (this.debug) {
          this.log('SSE heartbeat check', {
            timeSinceLastEvent,
            remainingTime: this.TIMEOUT_DURATION - timeSinceLastEvent,
            isConnected: this.isConnected,
            readyState: this.eventSource?.readyState
          });
        }
      }
    }, this.HEARTBEAT_INTERVAL);

    this.log('Timeout timer started', {
      timeoutDuration: this.TIMEOUT_DURATION,
      heartbeatInterval: this.HEARTBEAT_INTERVAL
    });
  }

  /**
   * Clear timeout timer
   */
  private clearTimeoutTimer(): void {
    if (this.timeoutTimer) {
      clearInterval(this.timeoutTimer);
      this.timeoutTimer = null;
      this.log('Timeout timer cleared');
    }
  }

  /**
   * Reset timeout timer khi nhận được event
   */
  private resetTimeoutTimer(): void {
    this.lastEventTime = Date.now();
    this.log('Timeout timer reset', { lastEventTime: new Date(this.lastEventTime).toISOString() });
  }

  /**
   * Kết nối tới SSE endpoint
   * Endpoint: GET /v1/chat/stream/events/{threadId}/{runId}
   */
  async connect(threadId: string, runId: string): Promise<void> {
    console.log('Connecting to SSE', { threadId, runId });

    // Disconnect existing connection
    // this.disconnect();

    // Store connection info
    this.currentThreadId = threadId;
    this.currentRunId = runId;

    // Tạo URL endpoint theo format mới
    const url = `${this.baseUrl}/v1/chat/stream/events/${threadId}/${runId}`;

    this.log('SSE URL', { url });

    try {

      this.log('Creating EventSource with URL', { url });

      this.eventSource = new EventSource(url, {
        withCredentials: true
      });

      // Set up event listeners
      this.setupEventListeners();

      // Start timeout timer
      this.startTimeoutTimer();

    } catch (error) {
      this.log('Failed to connect SSE', error);
      this.callbacks.onError?.(new Error('Failed to connect to SSE'));
    }
  }

  /**
   * Setup event listeners cho EventSource
   */
  private setupEventListeners(): void {
    if (!this.eventSource) return;

    // Connection opened
    this.eventSource.onopen = () => {
      this.log('SSE connection opened successfully');
      this.isConnected = true;
      this.resetTimeoutTimer(); // Reset timeout khi connection mở
    };

    // Connection error
    this.eventSource.onerror = (event) => {
      this.log('SSE connection error details', {
        readyState: this.eventSource?.readyState,
        url: this.eventSource?.url,
        event
      });
      this.isConnected = false;

      // Kiểm tra readyState để hiểu lỗi
      if (this.eventSource?.readyState === EventSource.CLOSED) {
        this.log('SSE connection was closed by server');
        this.callbacks.onError?.(new Error('SSE connection closed by server'));
      } else if (this.eventSource?.readyState === EventSource.CONNECTING) {
        this.log('SSE connection is retrying...');
        // Không gọi onError ngay, để browser retry
      } else {
        this.log('SSE connection failed');
        this.callbacks.onError?.(new Error('SSE connection failed'));
      }
    };

    // Generic message handler - xử lý tất cả events
    this.eventSource.onmessage = (event) => {
      console.log('[SSE] 🔥 RAW MESSAGE RECEIVED:', {
        data: event.data,
        type: event.type,
        lastEventId: event.lastEventId
      });
      this.log('Received SSE message', {
        data: event.data,
        type: event.type,
        lastEventId: event.lastEventId
      });

      // Reset timeout timer khi nhận được bất kỳ event nào
      this.resetTimeoutTimer();

      this.handleSSEEvent(event);
    };

    // Specific event handlers cho named events (nếu server gửi)
    this.eventSource.addEventListener('connected', (event) => {
      this.log('Received named connected event');
      this.handleConnectedEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('stream_text_token', (event) => {
      this.log('Received named stream_text_token event');
      this.handleTextTokenEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('stream_tool_token', (event) => {
      this.log('Received named stream_tool_token event');
      this.handleToolTokenEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('tool_call_start', (event) => {
      this.log('Received named tool_call_start event');
      this.handleToolCallStartEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('tool_call_end', (event) => {
      this.log('Received named tool_call_end event');
      this.handleToolCallEndEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('llm_stream_end', (event) => {
      this.log('Received named llm_stream_end event');
      this.handleLLMStreamEndEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('message_created', (event) => {
      this.log('Received named message_created event');
      this.handleMessageCreatedEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('stream_session_end', (event) => {
      this.log('Received named stream_session_end event');
      this.handleStreamSessionEndEvent(event as MessageEvent);
    });

    this.eventSource.addEventListener('stream_error', (event) => {
      this.log('Received named stream_error event');
      this.handleStreamErrorEvent(event as MessageEvent);
    });
  }

  /**
   * Xử lý SSE event chung - route events dựa trên event type
   */
  private handleSSEEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Parsed SSE event data', eventData);

      // Route events dựa trên event field
      if (eventData.event) {
        console.log('[SSE] 🎯 Routing event:', eventData.event);
        switch (eventData.event) {
          case 'stream_text_token':
            console.log('[SSE] 📝 Routing to text token handler');
            this.log('Routing to text token handler');
            this.handleTextTokenEvent(event);
            break;
          case 'stream_tool_token':
            this.log('Routing to tool token handler');
            this.handleToolTokenEvent(event);
            break;
          case 'tool_call_start':
            console.log('[SSE] 🤔 Routing to tool call start handler');
            this.log('Routing to tool call start handler');
            this.handleToolCallStartEvent(event);
            break;
          case 'tool_call_end':
            console.log('[SSE] ✅ Routing to tool call end handler');
            this.log('Routing to tool call end handler');
            this.handleToolCallEndEvent(event);
            break;
          case 'llm_stream_end':
            console.log('[SSE] 🏁 Routing to LLM stream end handler');
            this.log('Routing to LLM stream end handler');
            this.handleLLMStreamEndEvent(event);
            break;
          case 'message_created':
            console.log('[SSE] 📝 Routing to message created handler');
            this.log('Routing to message created handler');
            this.handleMessageCreatedEvent(event);
            break;
          case 'stream_session_end':
            console.log('[SSE] 🔚 Routing to stream session end handler');
            this.log('Routing to stream session end handler');
            this.handleStreamSessionEndEvent(event);
            break;
          case 'stream_error':
            console.log('[SSE] 🚨 Routing to stream error handler');
            this.log('Routing to stream error handler');
            this.handleStreamErrorEvent(event);
            break;
          case 'update_rpoint':
            console.log('[SSE] 🪙 Routing to update rpoint handler');
            this.handleUpdateRPointEvent(event);
            break;
          default:
            this.log('Unknown event type', { eventType: eventData.event });
        }
      } else if (eventData.threadId && eventData.from) {
        // Connected event format: {"threadId":"...","from":"live","timestamp":...}
        this.log('Routing to connected handler');
        this.handleConnectedEvent(event);
      } else {
        this.log('Unknown SSE event format', eventData);
      }
    } catch (error) {
      this.log('Failed to parse SSE event', { data: event.data, error });
    }
  }

  /**
   * Xử lý connected event
   */
  private handleConnectedEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Connected event data', eventData);

      // Format: {"threadId":"...","from":"live","timestamp":...}
      this.callbacks.onConnected?.(eventData);
    } catch (error) {
      this.log('Failed to parse connected event', error);
    }
  }

  /**
   * Xử lý stream_text_token event với Queue Support
   */
  private handleTextTokenEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Text token event data', eventData);

      // Xử lý format: {"event":"stream_text_token","data":{"role":"supervisor","text":"Hi"},"timestamp":...}
      let text: string = '';
      let role: string = '';

      if (eventData.data && typeof eventData.data === 'object') {
        text = eventData.data.text || '';
        role = eventData.data.role || '';
      }

      if (text && role) {
        console.log('[SSE] 🚀 Processing text token:', { text, role, useQueue: this.useQueueMode });

        if (this.useQueueMode && this.streamingController && this.currentMessageId) {
          // Queue-based streaming
          const token: TokenQueueItem = {
            text,
            role,
            messageId: this.currentMessageId,
            timestamp: Date.now(),
            priority: 1,
            metadata: {
              runId: this.currentRunId || undefined,
              threadId: this.currentThreadId || undefined,
              tokenIndex: this.tokenIndex++,
              isLastToken: false // Will be updated when stream ends
            }
          };

          const success = this.streamingController.addToken(token);
          if (success) {
            this.callbacks.onTokenQueued?.(token);
            console.log('[SSE] ✅ Token queued successfully');
          } else {
            console.log('[SSE] ❌ Failed to queue token, falling back to direct streaming');
            this.callbacks.onTextToken?.(text, role);
          }
        } else {
          // Direct streaming (fallback mode)
          console.log('[SSE] 🔄 Using direct streaming mode');
          this.callbacks.onTextToken?.(text, role);
        }

        this.log('Text token processed', { text, role, mode: this.useQueueMode ? 'queue' : 'direct' });
      } else {
        console.log('[SSE] ❌ Missing text or role:', { text, role, eventData });
      }
    } catch (error) {
      this.log('Failed to parse text token event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Xử lý stream_tool_token event
   */
  private handleToolTokenEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Tool token event data', eventData);

      // Xử lý format: {"event":"stream_tool_token","data":{"role":"supervisor"},"timestamp":...}
      let role: string = '';

      if (eventData.data && typeof eventData.data === 'object') {
        role = eventData.data.role || '';
      }

      if (role) {
        this.log('Tool token', { role });
        this.callbacks.onToolToken?.(role);
      }
    } catch (error) {
      this.log('Failed to parse tool token event', error);
    }
  }

  /**
   * Xử lý tool_call_start event
   */
  private handleToolCallStartEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Tool call start event data', eventData);

      // Xử lý format: {"event":"tool_call_start","data":{"role":"supervisor"},"timestamp":...}
      const role = eventData.data?.role || '';

      console.log('[SSE] 🤔 Tool call started for role:', role);
      this.callbacks.onToolCallStart?.(role);
      this.log('Tool call started', { role });
    } catch (error) {
      this.log('Failed to parse tool call start event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Xử lý tool_call_end event
   */
  private handleToolCallEndEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Tool call end event data', eventData);

      // Xử lý format: {"event":"tool_call_end","data":{"role":"supervisor"},"timestamp":...}
      const role = eventData.data?.role || '';

      console.log('[SSE] ✅ Tool call ended for role:', role);
      this.callbacks.onToolCallEnd?.(role);
      this.log('Tool call ended', { role });
    } catch (error) {
      this.log('Failed to parse tool call end event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Xử lý llm_stream_end event
   */
  private handleLLMStreamEndEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('LLM stream end event data', eventData);

      // Xử lý format: {"event":"llm_stream_end","data":{"role":"supervisor"},"timestamp":...}
      const role = eventData.data?.role || '';

      console.log('[SSE] 🏁 LLM stream ended for role:', role);

      // Force complete streaming for this role
      if (this.useQueueMode && this.streamingController) {
        this.streamingController.forceCompleteStreaming();
      }

      this.callbacks.onLLMStreamEnd?.(role);
      this.log('LLM stream ended', { role });
    } catch (error) {
      this.log('Failed to parse LLM stream end event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Xử lý message_created event
   */
  private handleMessageCreatedEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Message created event data', eventData);

      // Xử lý format: {"event":"message_created","data":{"message_id":"...","thread_id":"...","role":"assistant","content_preview":"...","has_token_data":false,"partial":false,"timestamp":...},"timestamp":...}
      const messageData = eventData.data;
      const messageId = messageData?.message_id || '';
      const threadId = messageData?.thread_id || '';
      const role = messageData?.role || '';
      const contentPreview = messageData?.content_preview || '';

      console.log('[SSE] 📝 Message created:', { messageId, threadId, role, contentPreview });
      this.callbacks.onMessageCreated?.(messageId, threadId, role, contentPreview);
      this.log('Message created', { messageId, threadId, role, contentPreview });
    } catch (error) {
      this.log('Failed to parse message created event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Xử lý stream_session_end event
   */
  private handleStreamSessionEndEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      this.log('Stream session end event data', eventData);

      // Xử lý format: {"event":"stream_session_end","data":{"reason":"Processing complete"},"timestamp":...}
      const reason = eventData.data?.reason || 'Session ended';

      console.log('[SSE] 🔚 Stream session ended:', reason);

      // Kết thúc session SSE - gọi callback onStreamEnd với thông tin hiện tại
      const threadId = this.currentThreadId || '';
      const runId = this.currentRunId || '';

      this.callbacks.onStreamSessionEnd?.(reason);
      this.callbacks.onStreamEnd?.(threadId, runId);
      this.log('Stream session ended', { reason, threadId, runId });

      // Clear timeout timer trước khi disconnect
      this.clearTimeoutTimer();

      // Auto disconnect sau khi session end
      this.disconnect();
    } catch (error) {
      this.log('Failed to parse stream session end event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Xử lý stream_error event
   */
  private handleStreamErrorEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      console.log('[SSE] 🚨 STREAM ERROR EVENT RECEIVED:', eventData);
      console.log('[SSE] 🚨 Raw event data:', event.data);
      this.log('Stream error event data', eventData);

      // Xử lý format từ server: {"event":"stream_error","data":{"error":"...","errorName":"...","stack":"..."}}
      let errorMessage: string = 'Unknown stream error';
      let errorDetails: unknown = null;

      if (eventData.data && typeof eventData.data === 'object') {
        // Lấy error message từ data.error
        errorMessage = eventData.data.error || eventData.data.message || 'Stream error occurred';
        // Lưu toàn bộ data làm details
        errorDetails = {
          errorName: eventData.data.errorName,
          stack: eventData.data.stack,
          threadId: eventData.data.threadId,
          runId: eventData.data.runId,
          timestamp: eventData.data.timestamp,
          fullData: eventData.data
        };
      } else if (typeof eventData.data === 'string') {
        errorMessage = eventData.data;
        errorDetails = { rawData: eventData.data };
      }

      console.log('[SSE] 🚨 PROCESSED ERROR:', { errorMessage, errorDetails });
      console.log('[SSE] 🚨 CALLBACK EXISTS:', !!this.callbacks.onStreamError);

      if (this.callbacks.onStreamError) {
        console.log('[SSE] 🚨 CALLING onStreamError callback...');
        this.callbacks.onStreamError(errorMessage, errorDetails);
        console.log('[SSE] 🚨 onStreamError callback called successfully');
      } else {
        console.error('[SSE] 🚨 NO onStreamError callback registered!');
      }

      this.log('Stream error', { errorMessage, errorDetails });

      // Disconnect connection sau khi có error
      console.log('[SSE] 🚨 Disconnecting after stream error');
      this.disconnect();
    } catch (error) {
      console.error('[SSE] 🚨 Failed to parse stream error event:', error);
      console.error('[SSE] 🚨 Raw event data that failed:', event.data);
      this.log('Failed to parse stream error event', error);
      this.callbacks.onStreamError?.('Failed to parse stream error event', { parseError: error, rawData: event.data });
    }
  }

  /**
   * Disconnect SSE connection
   */
  disconnect(): void {
    // Clear timeout timer trước khi disconnect
    this.clearTimeoutTimer();

    if (this.eventSource) {
      this.log('Disconnecting SSE');
      this.eventSource.close();
      this.eventSource = null;
      this.isConnected = false;
      this.currentThreadId = null;
      this.currentRunId = null;
      this.callbacks.onClose?.();
    }
  }

  /**
   * Set callbacks cho SSE events với Enhanced Support
   */
  setCallbacks(callbacks: EnhancedSSECallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Xử lý update_rpoint event
   */
  private handleUpdateRPointEvent(event: MessageEvent): void {
    try {
      const eventData = JSON.parse(event.data);
      console.log('[SSE] 🪙 UPDATE RPOINT EVENT RECEIVED:', eventData);
      this.log('Update RPoint event data', eventData);

      // Xử lý format: {"event":"update_rpoint","data":{"rPointCost":1396,"updatedBalance":"999599026","timestamp":1749626848053},"timestamp":1749626848053}
      const rPointData = eventData.data;
      const rPointCost = rPointData?.rPointCost || 0;
      const updatedBalance = rPointData?.updatedBalance || '0';
      const timestamp = rPointData?.timestamp || Date.now();

      console.log('[SSE] 🪙 RPoint update:', { rPointCost, updatedBalance, timestamp });
      this.callbacks.onUpdateRPoint?.(rPointCost, updatedBalance, timestamp);
      this.log('RPoint updated', { rPointCost, updatedBalance, timestamp });
    } catch (error) {
      this.log('Failed to parse update rpoint event', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.clearTimeoutTimer(); // Clear timeout timer
    this.disconnect();
    if (this.streamingController) {
      this.streamingController.destroy();
      this.streamingController = null;
    }
    this.log('ChatSSEService destroyed');
  }
  
  /**
   * Update base URL
   */
  updateBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
    this.log('Base URL updated', { baseUrl });
  }

  /**
   * Update debug mode
   */
  updateDebug(debug: boolean): void {
    this.debug = debug;
  }

  /**
   * Lấy trạng thái connection
   */
  getConnectionStatus(): {
    isConnected: boolean;
    threadId: string | null;
    runId: string | null;
    lastEventTime: number;
    timeSinceLastEvent: number;
    timeoutDuration: number;
  } {
    const now = Date.now();
    return {
      isConnected: this.isConnected,
      threadId: this.currentThreadId,
      runId: this.currentRunId,
      lastEventTime: this.lastEventTime,
      timeSinceLastEvent: now - this.lastEventTime,
      timeoutDuration: this.TIMEOUT_DURATION
    };
  }
}
