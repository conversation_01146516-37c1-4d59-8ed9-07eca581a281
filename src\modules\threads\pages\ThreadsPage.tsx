/**
 * ThreadsPage Component
 * Trang chính quản lý danh sách threads với grid layout
 */

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';

import { useChatStream } from '@/shared/hooks/common/useChatStream';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ThreadsGrid } from '../components/ThreadsGrid';


import { useChatIntegration } from '../hooks/useChatIntegration';

import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { chatConfigService } from '@/shared/services/chat-config.service';
import { useAppDispatch, useAppSelector } from '@/shared/store';
import { setCurrentThread, setThreadSwitching, updateThreadName, clearEvents } from '@/shared/store/slices/threadIntegrationSlice';
import { useQueryClient } from '@tanstack/react-query';

interface ThreadsPageProps {
  /**
   * Callback khi thread được started
   */
  onThreadStarted?: (threadId: string) => void;

  /**
   * Callback khi thread được deleted
   */
  onThreadDeleted?: (threadId: string) => void;

  /**
   * Callback khi thread name được changed
   */
  onThreadNameChanged?: (threadId: string, newName: string) => void;

  /**
   * Callback khi tạo thread mới
   */
  onCreateNewThread?: () => void;

  /**
   * Enable chat integration để track active thread
   */
  enableChatIntegration?: boolean;

  /**
   * Chat configuration (required if enableChatIntegration is true)
   */
  chatConfig?: {
    agentId?: string;
    apiBaseUrl?: string;
    sseBaseUrl?: string;
    getAuthToken?: () => string | Promise<string>;
    debug?: boolean;
  };

  /**
   * External chat stream instance để integrate
   */
  externalChatStream?: {
    updateThreadName?: (threadId: string, newName: string) => Promise<void>;
  };
}

/**
 * Component trang quản lý threads
 */
export const ThreadsPage: React.FC<ThreadsPageProps> = ({
  onThreadStarted,
  onThreadDeleted,
  onThreadNameChanged,
  enableChatIntegration = false,
  chatConfig: providedChatConfig,
  externalChatStream
}) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('updatedAt');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('DESC');

  // Auth để lấy token
  const { getToken } = useAuthCommon();

  // Redux for thread integration
  const dispatch = useAppDispatch();
  const threadIntegration = useAppSelector(state => state.threadIntegration);

  // Query client for cache invalidation
  const queryClient = useQueryClient();

  // Chat config - use provided or get from service
  const chatConfig = providedChatConfig || (enableChatIntegration ? chatConfigService.getConfig() : undefined);

  // Debug effect
  useEffect(() => {
    console.log('[ThreadsPage] Component mounted, checking state:', {
      searchTerm,
      sortBy,
      sortDirection,
      enableChatIntegration,
      hasProvidedChatConfig: !!providedChatConfig,
      hasChatConfig: !!chatConfig,
      currentThreadId: threadIntegration.currentThreadId
    });
  }, [searchTerm, sortBy, sortDirection, enableChatIntegration, providedChatConfig, chatConfig, threadIntegration.currentThreadId]);

  // Listen to Redux thread events
  useEffect(() => {
    const events = threadIntegration.events;

    // Handle thread creation
    if (events.threadCreated) {
      console.log('[ThreadsPage] New thread created:', events.threadCreated);

      // Force invalidate và refetch list queries để refresh danh sách ngay lập tức
      queryClient.invalidateQueries({
        queryKey: ['threads'],
        predicate: (query) => {
          const key = query.queryKey;
          const isListQuery = key.includes('list') || key.includes('paginated') || key.includes('infinite');
          const isDetailQuery = key.includes('detail');
          return isListQuery && !isDetailQuery;
        },
        refetchType: 'active' // Force refetch active queries ngay lập tức
      });

      // Đặc biệt invalidate infinite queries để đảm bảo ThreadsGrid refresh
      queryClient.invalidateQueries({
        queryKey: ['threads', 'infinite'],
        refetchType: 'active'
      });
      console.log('[ThreadsPage] Force invalidated and refetched threads queries after thread creation');
      console.log('[ThreadsPage] Also invalidated infinite queries specifically for ThreadsGrid');

      // Clear event
      dispatch(clearEvents());
    }

    // Handle dataset conversation creation
    if (events.datasetConversationCreated) {
      console.log('[ThreadsPage] New dataset conversation created:', events.datasetConversationCreated);

      // Force invalidate và refetch list queries để refresh danh sách threads ngay lập tức
      queryClient.invalidateQueries({
        queryKey: ['threads'],
        predicate: (query) => {
          const key = query.queryKey;
          const isListQuery = key.includes('list') || key.includes('paginated') || key.includes('infinite');
          const isDetailQuery = key.includes('detail');
          return isListQuery && !isDetailQuery;
        },
        refetchType: 'active' // Force refetch active queries ngay lập tức
      });

      // Đặc biệt invalidate infinite queries để đảm bảo ThreadsGrid refresh
      queryClient.invalidateQueries({
        queryKey: ['threads', 'infinite'],
        refetchType: 'active'
      });
      console.log('[ThreadsPage] Force invalidated and refetched threads queries after dataset conversation creation');
      console.log('[ThreadsPage] Also invalidated infinite queries specifically for ThreadsGrid');

      // Clear event
      dispatch(clearEvents());
    }

    // Handle thread updates
    if (events.threadUpdated) {
      console.log('[ThreadsPage] Thread updated:', events.threadUpdated);

      // Chỉ invalidate list queries để refresh thread name trong list
      queryClient.invalidateQueries({
        queryKey: ['threads'],
        predicate: (query) => {
          return query.queryKey.includes('list') || query.queryKey.includes('paginated');
        }
      });
      console.log('[ThreadsPage] Invalidated threads queries after thread update');

      // Clear event
      dispatch(clearEvents());
    }

    // Handle thread deletion
    if (events.threadDeleted) {
      console.log('[ThreadsPage] Thread deleted event received:', {
        deletedThreadId: events.threadDeleted,
        currentThreadId: threadIntegration.currentThreadId
      });

      // Remove deleted thread detail từ cache để tránh 404 errors
      queryClient.removeQueries({ queryKey: ['threads', 'detail', events.threadDeleted] });

      // Force invalidate tất cả list queries để refresh UI ngay lập tức
      queryClient.invalidateQueries({
        queryKey: ['threads'],
        predicate: (query) => {
          const key = query.queryKey;
          const isListQuery = key.includes('list') || key.includes('paginated') || key.includes('infinite');
          const isDetailQuery = key.includes('detail');
          return isListQuery && !isDetailQuery;
        },
        refetchType: 'active' // Force refetch active queries
      });
      console.log('[ThreadsPage] Force invalidated threads queries after thread deletion');

      // Clear event sau khi đã xử lý
      dispatch(clearEvents());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [threadIntegration.events, dispatch, queryClient]); // threadIntegration.currentThreadId chỉ dùng cho logging

  // Chat stream hook để track active thread (conditional)
  const chatStream = useChatStream(
    enableChatIntegration && chatConfig
      ? {
        agentId: chatConfig.agentId || 'default-agent',
        apiBaseUrl: chatConfig.apiBaseUrl || import.meta.env['VITE_API_URL'] + '/api',
        sseBaseUrl: chatConfig.sseBaseUrl || import.meta.env['VITE_API_URL'] + '/api',
        getAuthToken: () => getToken() || '',
        debug: chatConfig.debug || false,
        alwaysApproveToolCall: false,
        messageHistory: {
          pageSize: 20,
          autoLoad: true,
          timeout: 5000
        }
      }
      : {
        agentId: 'dummy',
        apiBaseUrl: 'dummy',
        sseBaseUrl: 'dummy',
        getAuthToken: () => '',
        debug: false
      }
  );

  // Chat integration hook
  const chatIntegration = useChatIntegration({
    chatStream: enableChatIntegration && chatConfig ? chatStream : null,
    enabled: enableChatIntegration && !!chatConfig
  });

  // Get current active thread ID from Redux (primary) or chatStream (fallback)
  const activeThreadId = threadIntegration.currentThreadId ||
    (enableChatIntegration ? chatStream.getCurrentThreadId() : null);

  // Xử lý thread started
  const handleThreadStarted = useCallback(async (threadId: string) => {
    console.log('[ThreadsPage] Thread started:', threadId);

    // Find thread info from current threads list
    // Note: This assumes we have access to threads list here
    // You might need to pass threads as dependency or get from a ref

    // Set current thread in Redux immediately với proper thread info
    console.log('[ThreadsPage] Dispatching setCurrentThread for threadId:', threadId);
    dispatch(setCurrentThread({
      threadId,
      threadName: `Thread ${threadId.slice(-8)}`, // Fallback name
      isNew: false
    }));

    // Set thread switching state
    console.log('[ThreadsPage] Dispatching setThreadSwitching(true)');
    dispatch(setThreadSwitching(true));

    // Sử dụng chat integration service để load thread
    if (chatIntegration.isIntegrationAvailable) {
      try {
        console.log('[ThreadsPage] Loading thread via chat integration:', threadId);
        await chatIntegration.loadThreadToChat(threadId);
        console.log('[ThreadsPage] Successfully loaded thread via chat integration:', threadId);

        // Thread switching state sẽ được clear trong onThreadLoaded event
      } catch (error) {
        console.error('[ThreadsPage] Failed to load thread via chat integration:', error);
        dispatch(setThreadSwitching(false));
      }
    } else {
      // Nếu không có chat integration, trigger manual switch
      console.log('[ThreadsPage] No chat integration, triggering manual switch');

      // TODO: Implement thread switching through Redux actions
      // For now, we'll handle this through chat integration

      // Set thread switching state sẽ được clear khi ChatPanel load xong
    }

    // Gọi callback từ parent
    onThreadStarted?.(threadId);
  }, [onThreadStarted, chatIntegration, dispatch]);

  // Handle thread name changed
  const handleThreadNameChanged = useCallback((threadId: string, newName: string) => {
    console.log('[ThreadsPage] Thread name changed:', { threadId, newName });

    // Update thread name in Redux
    dispatch(updateThreadName({ threadId, newName }));

    // Gọi callback từ parent
    onThreadNameChanged?.(threadId, newName);

    // Nếu có external chat stream, update thread name
    if (externalChatStream && externalChatStream.updateThreadName) {
      externalChatStream.updateThreadName(threadId, newName).catch((error: unknown) => {
        console.error('[ThreadsPage] Failed to update external chat stream thread name:', error);
      });
    }
  }, [dispatch, onThreadNameChanged, externalChatStream]);

  // Xử lý thread deleted
  const handleThreadDeleted = useCallback(async (threadId: string) => {
    console.log('[ThreadsPage] Thread deleted callback triggered:', {
      threadId,
      currentThreadId: threadIntegration.currentThreadId,
      isCurrentThread: threadId === threadIntegration.currentThreadId,
      enableChatIntegration
    });

    // Thread deletion đã được xử lý trong hooks (useActiveThreadDelete hoặc useSimpleThreadDelete)
    // Redux state đã được update, chỉ cần trigger external callback

    console.log('[ThreadsPage] Thread deletion handled by hooks, triggering external callback');

    // Gọi callback từ parent
    onThreadDeleted?.(threadId);
  }, [onThreadDeleted, threadIntegration.currentThreadId, enableChatIntegration]);

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // Tạo menu items cho sort
  const sortMenuItems: ModernMenuItem[] = [
    {
      id: 'updatedAt',
      label: t('threads:threads.list.sort.updated', 'Cập nhật'),
      onClick: () => {
        if (sortBy === 'updatedAt') {
          setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
        } else {
          setSortBy('updatedAt');
          setSortDirection('DESC');
        }
      },
      icon: sortBy === 'updatedAt' ? (sortDirection === 'DESC' ? 'arrow-down' : 'arrow-up') : undefined,
    },
    {
      id: 'createdAt',
      label: t('threads:threads.list.sort.created', 'Tạo'),
      onClick: () => {
        if (sortBy === 'createdAt') {
          setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
        } else {
          setSortBy('createdAt');
          setSortDirection('DESC');
        }
      },
      icon: sortBy === 'createdAt' ? (sortDirection === 'DESC' ? 'arrow-down' : 'arrow-up') : undefined,
    },
    {
      id: 'name',
      label: t('threads:threads.list.sort.name', 'Tên'),
      onClick: () => {
        if (sortBy === 'name') {
          setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
        } else {
          setSortBy('name');
          setSortDirection('DESC');
        }
      },
      icon: sortBy === 'name' ? (sortDirection === 'DESC' ? 'arrow-down' : 'arrow-up') : undefined,
    },
  ];

  return (
    <div className="h-full flex flex-col relative">
      {/* MenuIconBar */}
      <div className="flex-shrink-0 p-6">
        <MenuIconBar
          onSearch={handleSearch}
          items={sortMenuItems}
          showDateFilter={false}
          showColumnFilter={false}
        />
      </div>

      {/* Main Content - Fixed Height Box with Scroll */}
      <div className="px-6 pb-6">
        <div className="h-[calc(100vh-200px)]">
          <div
            className="h-full overflow-y-auto overflow-x-hidden p-6 scroll-smooth scrollbar-hide"
            id="threads-scroll-container"
          >

            <ThreadsGrid
              searchTerm={searchTerm}
              sortBy={sortBy}
              sortDirection={sortDirection}
              pageSize={20}
              onThreadStarted={handleThreadStarted}
              onThreadDeleted={handleThreadDeleted}
              onThreadNameChanged={handleThreadNameChanged}
              enableChatIntegration={enableChatIntegration}
              chatConfig={enableChatIntegration ? chatConfig : undefined}
              activeThreadId={activeThreadId}
              {...(enableChatIntegration && chatConfig && {
                externalChatStream: {
                  switchToThread: chatStream.switchToThread,
                  createNewThread: chatStream.createNewThread
                }
              })}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
