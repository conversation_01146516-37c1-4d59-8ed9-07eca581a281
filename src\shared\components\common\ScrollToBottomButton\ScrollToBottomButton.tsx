/**
 * <PERSON><PERSON>ToBottomButton Component
 * Button để scroll xuống dưới cùng của container
 */

import React, { useState, useEffect } from 'react';

interface ScrollToBottomButtonProps {
  /**
   * ID của container cần scroll
   */
  containerId: string;

  /**
   * Hiển thị button khi scroll lên trên bao nhiêu px
   */
  showAfter?: number;

  /**
   * Vị trí button
   */
  position?: {
    top?: string;
    bottom?: string;
    right?: string;
    left?: string;
    transform?: string;
  };

  /**
   * Kích thước button
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Custom className
   */
  className?: string;

  /**
   * Click handler tùy chỉnh
   */
  onClick?: () => void;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
  containerId,
  showAfter = 200,
  position = { bottom: '80px', right: '24px' },
  size = 'md',
  className = '',
  onClick
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Size configurations
  const config = {
    sm: { button: 'w-10 h-10', icon: 'w-4 h-4' },
    md: { button: 'w-12 h-12', icon: 'w-5 h-5' },
    lg: { button: 'w-14 h-14', icon: 'w-6 h-6' }
  }[size];

  useEffect(() => {
    const handleScroll = () => {
      const container = containerId ? document.getElementById(containerId) : window;
      
      if (!container) return;

      const scrollTop = containerId 
        ? (container as HTMLElement).scrollTop
        : window.pageYOffset || document.documentElement.scrollTop;

      const scrollHeight = containerId
        ? (container as HTMLElement).scrollHeight
        : document.documentElement.scrollHeight;

      const clientHeight = containerId
        ? (container as HTMLElement).clientHeight
        : window.innerHeight;

      // Hiển thị button khi:
      // 1. Scroll lên trên > showAfter px
      // 2. Không ở bottom của container
      const isScrolledUp = scrollTop > showAfter;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px tolerance
      
      setIsVisible(isScrolledUp && !isAtBottom);
    };

    // Setup scroll listener với retry logic
    const setupScrollListener = () => {
      const container = containerId ? document.getElementById(containerId) : window;
      
      if (container) {
        container.addEventListener('scroll', handleScroll, { passive: true });
        handleScroll(); // Check initial position
        return container;
      }
      return null;
    };

    // Try immediate setup
    let container = setupScrollListener();

    // Retry if container not found
    let retryTimeout: NodeJS.Timeout | null = null;
    if (!container && containerId) {
      retryTimeout = setTimeout(() => {
        container = setupScrollListener();
      }, 100);
    }

    return () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [containerId, showAfter]);

  const scrollToBottom = () => {
    if (onClick) {
      onClick();
      return;
    }

    const container = containerId ? document.getElementById(containerId) : window;
    if (container) {
      if (containerId) {
        // Scroll container to bottom
        (container as HTMLElement).scrollTo({
          top: (container as HTMLElement).scrollHeight,
          behavior: 'smooth'
        });
      } else {
        // Scroll window to bottom
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth'
        });
      }
    }
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={scrollToBottom}
      style={position}
      className={`
        absolute z-[9999] ${config.button}
        bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700
        text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300
        rounded-full shadow-lg hover:shadow-xl
        border border-gray-200 dark:border-gray-600
        transition-all duration-300 ease-in-out
        hover:scale-110 active:scale-95
        flex items-center justify-center
        pointer-events-auto
        backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95
        ${className}
      `}
      title="Scroll to bottom"
      aria-label="Scroll to bottom"
    >
      {/* Custom arrow down icon */}
      <svg
        className={`${config.icon}`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        strokeWidth={3}
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          d="M19 9l-7 7-7-7" 
        />
      </svg>
    </button>
  );
};

export default ScrollToBottomButton;
