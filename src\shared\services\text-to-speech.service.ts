/**
 * Text-to-Speech Service
 * Sử dụng Google Cloud Text-to-Speech API để chuyển đổi text thành giọng nói
 */

/**
 * Configuration cho Text-to-Speech
 */
export interface TextToSpeechConfig {
  projectId: string;
  apiKey: string;
  languageCode?: string;
  voiceName?: string;
  ssmlGender?: 'NEUTRAL' | 'FEMALE' | 'MALE';
  audioEncoding?: 'MP3' | 'LINEAR16' | 'OGG_OPUS' | 'MULAW' | 'ALAW';
  speakingRate?: number;
  pitch?: number;
  volumeGainDb?: number;
}

/**
 * Voice selection options
 */
export interface VoiceOption {
  name: string;
  languageCode: string;
  ssmlGender: 'NEUTRAL' | 'FEMALE' | 'MALE';
  naturalSampleRateHertz: number;
}

/**
 * Synthesis request
 */
export interface SynthesisRequest {
  text: string;
  voiceConfig?: Partial<TextToSpeechConfig>;
  priority?: number;
}

/**
 * Synthesis result
 */
export interface SynthesisResult {
  audioContent: string; // Base64 encoded audio
  audioUrl?: string; // Object URL for playback
  duration?: number;
}

/**
 * Callback functions cho TTS operations
 */
export interface TextToSpeechCallbacks {
  onSynthesisStart?: (text: string) => void;
  onSynthesisComplete?: (result: SynthesisResult) => void;
  onPlaybackStart?: () => void;
  onPlaybackEnd?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Audio queue item
 */
interface AudioQueueItem {
  id: string;
  request: SynthesisRequest;
  result?: SynthesisResult;
  audio?: HTMLAudioElement;
  status: 'pending' | 'synthesizing' | 'ready' | 'playing' | 'completed' | 'error';
  callbacks?: TextToSpeechCallbacks;
}

/**
 * Text-to-Speech Service Class
 */
export class TextToSpeechService {
  private config: TextToSpeechConfig;
  private audioQueue: AudioQueueItem[] = [];
  private currentAudio: HTMLAudioElement | null = null;
  private isPlaying = false;
  private isProcessing = false;

  constructor(config: TextToSpeechConfig) {
    this.config = {
      languageCode: 'vi-VN',
      voiceName: 'vi-VN-Standard-A',
      ssmlGender: 'FEMALE',
      audioEncoding: 'MP3',
      speakingRate: 1.0,
      pitch: 0.0,
      volumeGainDb: 0.0,
      ...config,
    };

    console.log('[TextToSpeech] Service initialized with config:', this.config);
  }

  /**
   * Synthesize text thành audio và play ngay lập tức
   */
  async speak(text: string, callbacks: TextToSpeechCallbacks = {}): Promise<void> {
    const request: SynthesisRequest = {
      text,
      priority: 1,
    };

    await this.addToQueue(request, callbacks);
  }

  /**
   * Synthesize text thành audio và trả về result (không play)
   */
  async synthesize(text: string, voiceConfig?: Partial<TextToSpeechConfig>): Promise<SynthesisResult> {
    try {
      console.log('[TextToSpeech] Synthesizing text:', text.substring(0, 100) + '...');

      const requestBody = {
        input: { text },
        voice: {
          languageCode: voiceConfig?.languageCode || this.config.languageCode,
          name: voiceConfig?.voiceName || this.config.voiceName,
          ssmlGender: voiceConfig?.ssmlGender || this.config.ssmlGender,
        },
        audioConfig: {
          audioEncoding: voiceConfig?.audioEncoding || this.config.audioEncoding,
          speakingRate: voiceConfig?.speakingRate || this.config.speakingRate,
          pitch: voiceConfig?.pitch || this.config.pitch,
          volumeGainDb: voiceConfig?.volumeGainDb || this.config.volumeGainDb,
        },
      };

      const response = await fetch(
        `https://texttospeech.googleapis.com/v1/text:synthesize?key=${this.config.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        throw new Error(`TTS API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.audioContent) {
        throw new Error('No audio content received from TTS API');
      }

      // Tạo object URL cho audio playback
      const audioBlob = this.base64ToBlob(result.audioContent, 'audio/mpeg');
      const audioUrl = URL.createObjectURL(audioBlob);

      const synthesisResult: SynthesisResult = {
        audioContent: result.audioContent,
        audioUrl,
      };

      console.log('[TextToSpeech] Synthesis completed successfully');
      return synthesisResult;

    } catch (error) {
      console.error('[TextToSpeech] Synthesis failed:', error);
      throw error;
    }
  }

  /**
   * Thêm request vào queue để xử lý tuần tự
   */
  async addToQueue(request: SynthesisRequest, callbacks: TextToSpeechCallbacks = {}): Promise<void> {
    const queueItem: AudioQueueItem = {
      id: `tts-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      request,
      status: 'pending',
      callbacks,
    };

    this.audioQueue.push(queueItem);
    console.log('[TextToSpeech] Added to queue:', queueItem.id);

    // Process queue if not already processing
    if (!this.isProcessing) {
      await this.processQueue();
    }
  }

  /**
   * Xử lý queue tuần tự
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.audioQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.audioQueue.length > 0) {
      const item = this.audioQueue.shift();
      if (!item) continue;

      try {
        await this.processQueueItem(item);
      } catch (error) {
        console.error('[TextToSpeech] Failed to process queue item:', error);
        item.callbacks?.onError?.(error as Error);
      }
    }

    this.isProcessing = false;
  }

  /**
   * Xử lý một item trong queue
   */
  private async processQueueItem(item: AudioQueueItem): Promise<void> {
    try {
      // Synthesize
      item.status = 'synthesizing';
      item.callbacks?.onSynthesisStart?.(item.request.text);

      const result = await this.synthesize(item.request.text, item.request.voiceConfig);
      item.result = result;
      item.status = 'ready';

      item.callbacks?.onSynthesisComplete?.(result);

      // Play audio
      if (result.audioUrl) {
        await this.playAudio(result.audioUrl, item.callbacks);
      }

      item.status = 'completed';

    } catch (error) {
      item.status = 'error';
      throw error;
    }
  }

  /**
   * Play audio từ URL
   */
  private async playAudio(audioUrl: string, callbacks?: TextToSpeechCallbacks): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Stop current audio if playing
        if (this.currentAudio) {
          this.currentAudio.pause();
          this.currentAudio = null;
        }

        const audio = new Audio(audioUrl);
        this.currentAudio = audio;
        this.isPlaying = true;

        audio.onloadeddata = () => {
          console.log('[TextToSpeech] Audio loaded, starting playback');
          callbacks?.onPlaybackStart?.();
        };

        audio.onended = () => {
          console.log('[TextToSpeech] Audio playback ended');
          this.isPlaying = false;
          this.currentAudio = null;
          callbacks?.onPlaybackEnd?.();
          
          // Cleanup object URL
          URL.revokeObjectURL(audioUrl);
          resolve();
        };

        audio.onerror = (error) => {
          console.error('[TextToSpeech] Audio playback error:', error);
          this.isPlaying = false;
          this.currentAudio = null;
          callbacks?.onError?.(new Error('Audio playback failed'));
          reject(error);
        };

        // Start playback
        audio.play().catch(error => {
          console.error('[TextToSpeech] Failed to start audio playback:', error);
          this.isPlaying = false;
          this.currentAudio = null;
          callbacks?.onError?.(error);
          reject(error);
        });

      } catch (error) {
        console.error('[TextToSpeech] Failed to setup audio playback:', error);
        callbacks?.onError?.(error as Error);
        reject(error);
      }
    });
  }

  /**
   * Dừng audio hiện tại
   */
  stopCurrentAudio(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio = null;
      this.isPlaying = false;
      console.log('[TextToSpeech] Current audio stopped');
    }
  }

  /**
   * Clear toàn bộ queue
   */
  clearQueue(): void {
    this.audioQueue = [];
    this.stopCurrentAudio();
    console.log('[TextToSpeech] Queue cleared');
  }

  /**
   * Convert base64 thành Blob
   */
  private base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * Getters
   */
  get isCurrentlyPlaying(): boolean {
    return this.isPlaying;
  }

  get queueLength(): number {
    return this.audioQueue.length;
  }

  /**
   * Cập nhật configuration
   */
  updateConfig(newConfig: Partial<TextToSpeechConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[TextToSpeech] Config updated:', this.config);
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.clearQueue();
    console.log('[TextToSpeech] Service disposed');
  }
}

/**
 * Factory function để tạo TextToSpeechService instance
 */
export function createTextToSpeechService(config: TextToSpeechConfig): TextToSpeechService {
  return new TextToSpeechService(config);
}

/**
 * Default export
 */
export default TextToSpeechService;
