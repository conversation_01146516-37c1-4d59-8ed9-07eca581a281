# Worker Delay & Timeout Fixes

## ✅ Đã Sửa

### **Vấn đề 1: Worker message biến mất ngay lập tức**
- **<PERSON><PERSON><PERSON> cầu**: Worker message hiển thị thêm 3 giây trước khi ẩn
- **<PERSON>uy<PERSON><PERSON> nhân**: Worker message bị ẩn ngay khi `llm_stream_end` với `collapsed: true`

### **Vấn đề 2: Timeout error khi đang chạy bình thường**
- **Y<PERSON><PERSON> cầu**: Không hiển thị timeout error khi đang streaming
- **Nguyên nhân**: SSE timeout logic quá aggressive và không check streaming state

## 🔧 Các Sửa Đổi

### **1. Worker Message Delay Logic**

#### **useChatStream.ts - onLLMStreamEnd**:
```typescript
if (role === 'worker') {
  console.log('[useChatStream] Worker stream ended - will collapse message after 3 seconds');
  
  // Đ<PERSON><PERSON> tiên, set status COMPLETED nhưng chưa collapse
  const completedMessage: ChatMessage = {
    ...currentStreamingMessageRef.current,
    status: MessageStatus.COMPLETED,
    metadata: {
      ...currentStreamingMessageRef.current.metadata,
      processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime()
    }
  };

  setMessages(prev => prev.map(msg =>
    msg.id === currentStreamingMessageRef.current?.id
      ? completedMessage
      : msg
  ));

  // Sau 3 giây, mới collapse message để ẩn
  setTimeout(() => {
    console.log('[useChatStream] Worker message delay completed - collapsing now');
    const collapsedMessage: ChatMessage = {
      ...completedMessage,
      metadata: {
        ...completedMessage.metadata,
        collapsed: true,
        hideAfterDelay: true
      }
    };

    setMessages(prev => prev.map(msg =>
      msg.id === completedMessage.id
        ? collapsedMessage
        : msg
    ));
  }, 3000); // 3 giây delay
}
```

#### **ChatMessage Metadata Type Update**:
```typescript
metadata?: {
  // ... existing fields
  /**
   * Đánh dấu message được ẩn sau delay (cho worker messages)
   */
  hideAfterDelay?: boolean;
};
```

### **2. Enhanced Message Filtering**

#### **ChatContent.tsx - shouldShowMessage**:
```typescript
const shouldShowMessage = (message: ChatMessageType): boolean => {
  if (message.sender === 'worker') {
    // Hiển thị worker messages khi:
    // 1. Đang streaming (status = STREAMING)
    // 2. Đã hoàn thành nhưng chưa collapsed (status = COMPLETED && !collapsed)
    const isCurrentlyStreaming = isStreaming && message.status === MessageStatus.STREAMING;
    const isCompletedButNotCollapsed = message.status === MessageStatus.COMPLETED && !message.metadata?.collapsed;
    const shouldShow = isCurrentlyStreaming || isCompletedButNotCollapsed;
    
    return shouldShow;
  }
  return true; // Hiển thị tất cả messages khác
};
```

### **3. Improved SSE Timeout Logic**

#### **ChatSSEService.ts - Enhanced Timeout Check**:
```typescript
// Chỉ trigger timeout nếu:
// 1. Không có events trong 1 phút
// 2. Connection vẫn đang active (isConnected = true)
// 3. EventSource vẫn đang open
if (timeSinceLastEvent >= this.TIMEOUT_DURATION && 
    this.isConnected && 
    this.eventSource?.readyState === EventSource.OPEN) {
  
  console.warn('[ChatSSEService] ⏰ SSE timeout: No events received for 1 minute, disconnecting...');
  this.callbacks.onError?.(new Error('SSE connection timeout: No events received for 1 minute'));
  this.disconnect();
} else {
  // Log heartbeat chỉ khi debug mode
  if (this.debug) {
    this.log('SSE heartbeat check', {
      timeSinceLastEvent,
      remainingTime: this.TIMEOUT_DURATION - timeSinceLastEvent,
      isConnected: this.isConnected,
      readyState: this.eventSource?.readyState
    });
  }
}
```

#### **Clear Timeout on Session End**:
```typescript
// handleStreamSessionEndEvent
// Clear timeout timer trước khi disconnect
this.clearTimeoutTimer();

// Auto disconnect sau khi session end
this.disconnect();
```

### **4. Smart Error Handling**

#### **useChatStream.ts - onError**:
```typescript
onError: (error: Error) => {
  console.error('[useChatStream] 🚨 SSE ERROR:', error);

  // Xử lý timeout error đặc biệt - chỉ hiển thị nếu thực sự timeout
  if (error.message.includes('timeout')) {
    console.warn('[useChatStream] ⏰ SSE timeout detected');
    
    // Kiểm tra xem có đang streaming không - nếu có thì có thể là false positive
    if (isStreaming || isLoading || isThinking) {
      console.warn('[useChatStream] ⚠️ Timeout detected but still streaming - ignoring timeout error');
      return; // Không set error nếu đang streaming
    }
    
    setError('Kết nối bị timeout sau 1 phút không có phản hồi. Vui lòng thử lại.');
  } else {
    setError(error.message);
  }

  // Reset states...
},
```

## 🎯 Luồng Hoạt Động Mới

### **Worker Message Lifecycle**:
```
1. Worker starts → status = STREAMING → Hiển thị
2. Worker streaming → Update content → Tiếp tục hiển thị
3. Worker ends → status = COMPLETED, collapsed = false → Hiển thị 3 giây
4. After 3 seconds → collapsed = true, hideAfterDelay = true → Ẩn
5. Supervisor starts → Hiển thị với agent avatar
```

### **SSE Timeout Management**:
```
1. Connect → Start timeout timer
2. Receive events → Reset timeout timer
3. No events for 60s + isConnected + readyState = OPEN → Trigger timeout
4. Stream session end → Clear timeout timer → Disconnect
5. Manual disconnect → Clear timeout timer
```

### **Error Handling Flow**:
```
1. Timeout detected → Check streaming state
2. If streaming → Ignore timeout (false positive)
3. If not streaming → Show timeout error
4. Other errors → Show error message
```

## 🔍 Debug & Monitoring

### **Enhanced Logging**:
- Worker message delay progress
- Timeout timer state changes
- Streaming state checks
- SSE connection status

### **Console Messages**:
```typescript
// Worker delay
'[useChatStream] Worker stream ended - will collapse message after 3 seconds'
'[useChatStream] Worker message delay completed - collapsing now'

// Timeout detection
'[ChatSSEService] ⏰ SSE timeout: No events received for 1 minute'
'[useChatStream] ⚠️ Timeout detected but still streaming - ignoring timeout error'

// Message filtering
'Worker message filter: shouldShow = true/false'
```

## 🧪 Test Scenarios

### **Worker Message Delay**:
- [ ] Worker starts → Message appears immediately
- [ ] Worker completes → Message stays visible for 3 seconds
- [ ] After 3 seconds → Message disappears
- [ ] Supervisor starts → New message with agent avatar

### **Timeout Prevention**:
- [ ] Normal streaming → No timeout errors
- [ ] Long processing → No false timeout
- [ ] Actual timeout (no events 60s) → Show timeout error
- [ ] Stream session end → Clear timeout properly

### **Error Handling**:
- [ ] Timeout during streaming → Ignored
- [ ] Timeout when idle → Show error
- [ ] Other SSE errors → Show appropriate message
- [ ] Manual disconnect → No timeout errors

## 🚀 Benefits

### **User Experience**:
- ✅ **Smooth Transitions**: Worker messages visible for 3 seconds
- ✅ **No False Alarms**: Timeout errors only when actually needed
- ✅ **Clear Feedback**: Proper error messages for real issues
- ✅ **Reliable Streaming**: No interruptions during normal operation

### **Technical Improvements**:
- ✅ **Smart Timeout**: Context-aware timeout detection
- ✅ **Proper Cleanup**: Timeout timers cleared appropriately
- ✅ **Enhanced Filtering**: Time-based message visibility
- ✅ **Robust Error Handling**: False positive prevention

### **Debug & Maintenance**:
- ✅ **Better Logging**: Clear debug information
- ✅ **State Tracking**: Comprehensive state monitoring
- ✅ **Error Context**: Detailed error information
- ✅ **Performance**: Reduced unnecessary logging

Hệ thống giờ đây hoạt động mượt mà với worker messages hiển thị 3 giây và không còn false timeout errors! 🎉
