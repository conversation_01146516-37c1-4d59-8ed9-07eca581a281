# Worker & Supervisor Message Implementation

## ✅ Đã Hoàn Thành

### **<PERSON><PERSON><PERSON> cầu**:
1. **Worker messages**: Hiển thị bình thường khi streaming, sau đó ẩn/xóa
2. **Supervisor messages**: Sử dụng avatar của Agent từ header thay vì icon mặc định
3. **Message filtering**: Chỉ hiển thị supervisor messages sau khi worker hoàn thành

## 🔧 Implementation Details

### **1. Worker Message Filtering**

#### **ChatContent.tsx - Message Filter Logic**:
```typescript
// Filter function để ẩn worker messages đã hoàn thành
const shouldShowMessage = (message: ChatMessageType): boolean => {
  // Hiển thị tất cả messages trừ worker messages đã hoàn thành
  if (message.sender === 'worker') {
    // Chỉ hiển thị worker messages khi đang streaming
    const isCurrentlyStreaming = isStreaming && message.status === MessageStatus.STREAMING;
    return isCurrentlyStreaming;
  }
  return true; // Hiển thị tất cả messages khác
};

// Apply filter to both history and current messages
...historyMessages.filter(shouldShowMessage).map(message => ({...}))
...messages.filter(shouldShowMessage).map(message => ({...}))
```

#### **Logic Flow**:
```
Worker Message Lifecycle:
1. Worker starts → message.status = STREAMING → Hiển thị
2. Worker processing → isStreaming = true → Tiếp tục hiển thị
3. Worker completes → message.status = COMPLETED → Ẩn message
4. Supervisor message → Hiển thị với agent avatar
```

### **2. Agent Avatar Integration**

#### **ChatHeader.tsx - Agent Selection**:
```typescript
interface ChatHeaderProps {
  // ... existing props
  onAgentChange?: (agent: AgentSimpleDto) => void;
}

// Notify parent khi agent được chọn
const agentItems = agents.map((agent: AgentSimpleDto) => ({
  onClick: () => {
    setSelectedAgent(agent);
    if (onAgentChange) {
      onAgentChange(agent); // Pass agent to parent
    }
  },
}));
```

#### **ChatPanel.tsx - Agent State Management**:
```typescript
// State cho selected agent
const [selectedAgent, setSelectedAgent] = useState<{
  id: string; 
  name: string; 
  avatar?: string 
} | null>(null);

// Handler cho agent change
const handleAgentChange = (agent: { id: string; name: string; avatar?: string }) => {
  setSelectedAgent(agent);
};

// Pass agent avatar to ChatContent
<ChatContent
  {...(selectedAgent?.avatar && { agentAvatar: selectedAgent.avatar })}
/>
```

#### **ChatContent.tsx - Avatar Assignment**:
```typescript
// Sử dụng agentAvatar cho supervisor, default avatar cho các role khác
{...(() => {
  const messageData = item.data as ChatMessageType;
  if (messageData.sender === 'supervisor' && agentAvatar) {
    return { avatar: agentAvatar };
  } else if (messageData.sender === 'ai') {
    return { avatar: '/assets/images/ai-agents/assistant-robot.svg' };
  }
  return {};
})()}
```

### **3. Enhanced Avatar Mapping**

#### **avatarMapping.ts - Custom Avatar Support**:
```typescript
/**
 * Lấy avatar config cho role
 * @param role - Chat role
 * @param customAvatar - Custom avatar URL (for supervisor using agent avatar)
 */
export const getAvatarForRole = (role: ChatRole, customAvatar?: string): AvatarConfig => {
  const config = ROLE_AVATAR_MAPPING[role] || ROLE_AVATAR_MAPPING.ai;
  
  // Sử dụng custom avatar nếu được cung cấp (chủ yếu cho supervisor)
  if (customAvatar && role === 'supervisor') {
    return {
      ...config,
      url: customAvatar
    };
  }
  
  return config;
};
```

#### **ChatMessage.tsx - Avatar Usage**:
```typescript
// Sử dụng avatar mapping utilities với custom avatar support
const avatarConfig = getAvatarForRole(sender, avatar);
const avatarUrl = avatar || avatarConfig.url;
```

## 🎯 Luồng Hoạt Động

### **Worker Message Flow**:
```
1. SSE Event: tool_call_start (role: worker)
   → Create worker message với status STREAMING
   → shouldShowMessage() = true → Hiển thị

2. SSE Event: stream_text_token (role: worker)
   → Update worker message content
   → isStreaming = true → Tiếp tục hiển thị

3. SSE Event: llm_stream_end (role: worker)
   → Set message status = COMPLETED
   → shouldShowMessage() = false → Ẩn message

4. SSE Event: stream_text_token (role: supervisor)
   → Create supervisor message
   → Use agentAvatar from header → Hiển thị
```

### **Agent Avatar Flow**:
```
1. ChatHeader loads agents → Set default agent
2. User selects agent → onAgentChange(agent)
3. ChatPanel updates selectedAgent state
4. ChatContent receives agentAvatar prop
5. Supervisor messages use agentAvatar
6. Other roles use default avatars
```

## 📊 Component Integration

### **Data Flow**:
```
ChatHeader (Agent Selection)
    ↓ onAgentChange
ChatPanel (Agent State)
    ↓ agentAvatar prop
ChatContent (Message Rendering)
    ↓ avatar prop
ChatMessage (Avatar Display)
```

### **Props Chain**:
```typescript
// ChatHeader → ChatPanel
onAgentChange: (agent: AgentSimpleDto) => void

// ChatPanel → ChatContent  
agentAvatar?: string

// ChatContent → ChatMessage
avatar?: string (for supervisor messages)
```

## 🔍 Message Filtering Logic

### **Visibility Rules**:
- ✅ **User messages**: Always visible
- ✅ **AI messages**: Always visible  
- ✅ **Supervisor messages**: Always visible + use agent avatar
- ⚠️ **Worker messages**: Only visible when streaming

### **Filter Implementation**:
```typescript
const shouldShowMessage = (message: ChatMessageType): boolean => {
  if (message.sender === 'worker') {
    // Worker chỉ hiển thị khi đang streaming
    return isStreaming && message.status === MessageStatus.STREAMING;
  }
  return true; // Tất cả messages khác luôn hiển thị
};
```

## 🎨 UI/UX Improvements

### **Avatar Consistency**:
- **Supervisor**: Agent avatar từ header (dynamic)
- **Worker**: Ẩn sau khi hoàn thành (clean UI)
- **AI**: Default assistant avatar (consistent)
- **User**: User avatar (personal)

### **Message Flow**:
- **Smooth Transitions**: Worker messages fade out khi hoàn thành
- **No Clutter**: Chỉ hiển thị final supervisor response
- **Visual Hierarchy**: Agent avatar tạo connection với header

## 🧪 Test Scenarios

### **Worker Message Lifecycle**:
- [ ] Worker starts → Message appears
- [ ] Worker streaming → Message updates
- [ ] Worker completes → Message disappears
- [ ] Supervisor starts → New message with agent avatar

### **Agent Avatar**:
- [ ] Default agent selected → Supervisor uses default avatar
- [ ] User selects agent → Supervisor uses selected agent avatar
- [ ] Agent change → New supervisor messages use new avatar
- [ ] Other roles unaffected by agent selection

### **Message Filtering**:
- [ ] Only supervisor messages visible after completion
- [ ] Worker messages hidden from history
- [ ] Clean message thread without worker clutter
- [ ] Proper message ordering maintained

## 🚀 Benefits

### **User Experience**:
- ✅ **Clean Interface**: No worker message clutter
- ✅ **Visual Consistency**: Agent avatar connection
- ✅ **Smooth Flow**: Only final responses visible
- ✅ **Personal Touch**: Agent selection affects appearance

### **Technical Benefits**:
- ✅ **Flexible Avatar System**: Support custom avatars
- ✅ **Efficient Filtering**: Performance-optimized message filtering
- ✅ **Maintainable Code**: Clear separation of concerns
- ✅ **Extensible Design**: Easy to add new message types

Hệ thống worker/supervisor message handling đã được implement hoàn chỉnh với UX tối ưu! 🎉
