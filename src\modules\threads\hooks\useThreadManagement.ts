/**
 * Thread Management Hook
 * Hook quản lý threads với chat integration
 */

import { useState, useCallback, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { ThreadsService } from '../services';
import { chatIntegrationService } from '../services/chat-integration.service';
import { THREADS_QUERY_KEYS } from '../constants';
import { useAppDispatch, useAppSelector } from '@/shared/store';
import { threadDeleted, setCurrentThread, setThreadSwitching } from '@/shared/store/slices/threadIntegrationSlice';
import { ThreadItem } from '@/shared/types/chat-streaming.types';

interface UseThreadManagementOptions {
  /**
   * Callback khi thread được load vào chat
   */
  onThreadStarted?: (threadId: string) => void;

  /**
   * Callback khi thread bị xóa
   */
  onThreadDeleted?: (threadId: string) => void;

  /**
   * <PERSON>h sách threads hiện tại (để tìm next thread khi delete)
   */
  availableThreads?: ThreadItem[];
}

/**
 * Hook quản lý thread operations
 */
export const useThreadManagement = ({
  onThreadStarted,
  onThreadDeleted,
  availableThreads = []
}: UseThreadManagementOptions = {}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const dispatch = useAppDispatch();

  // Get current thread from Redux state
  const threadIntegration = useAppSelector(state => state.threadIntegration);

  const [currentThreadId, setCurrentThreadId] = useState<string | null>(
    threadIntegration.currentThreadId || chatIntegrationService.getCurrentThreadId()
  );
  const [startingThreadId, setStartingThreadId] = useState<string | null>(null);
  const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

  // Sync local state với Redux state
  useEffect(() => {
    if (threadIntegration.currentThreadId !== currentThreadId) {
      console.log('[useThreadManagement] Syncing with Redux state:', {
        reduxThreadId: threadIntegration.currentThreadId,
        localThreadId: currentThreadId
      });
      setCurrentThreadId(threadIntegration.currentThreadId);
    }
  }, [threadIntegration.currentThreadId, currentThreadId]);

  // Sync với chat integration service
  useEffect(() => {
    const handleThreadLoaded = (threadId: string) => {
      setCurrentThreadId(threadId);
      // onThreadStarted callback đã được gọi trong mutation onSuccess
    };

    const handleThreadDeleted = (threadId: string) => {
      onThreadDeleted?.(threadId);
    };

    chatIntegrationService.setEventListeners({
      onThreadLoaded: handleThreadLoaded,
      onThreadDeleted: handleThreadDeleted,
    });
  }, [onThreadDeleted, dispatch]);

  // Mutation để start thread
  const startThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setStartingThreadId(threadId);

      // Dispatch thread switching state to Redux
      console.log('[useThreadManagement] Starting thread switch to:', threadId);
      dispatch(setThreadSwitching(true));

      await chatIntegrationService.loadThreadToChat(threadId);
      return threadId;
    },
    onSuccess: (threadId, threadName) => {
      setCurrentThreadId(threadId);
      setStartingThreadId(null);

      // Update Redux state với thread info
      console.log('[useThreadManagement] Thread started successfully:', threadId);
      dispatch(setCurrentThread({
        threadId,
        threadName: `${threadName.slice(-8)}`, // Fallback name, sẽ được update trong onThreadLoaded
        isNew: false
      }));

      // Trigger callback để notify parent components
      onThreadStarted?.(threadId);
    },
    onError: (error) => {
      console.error('Failed to start thread:', error);
      setStartingThreadId(null);
      dispatch(setThreadSwitching(false));
    },
  });

  // Mutation để delete thread
  const deleteThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setDeletingThreadId(threadId);

      console.log('[useThreadManagement] Deleting thread:', {
        threadId,
        currentThreadId,
        availableThreadsCount: availableThreads.length
      });

      // Xóa thread từ API
      await ThreadsService.deleteThread(threadId);

      // Xử lý chat integration và tìm next thread
      const nextThreadId = await chatIntegrationService.handleThreadDeleted(
        threadId,
        availableThreads
      );

      console.log('[useThreadManagement] Thread deleted, next thread:', {
        deletedThreadId: threadId,
        nextThreadId
      });

      return { deletedThreadId: threadId, nextThreadId };
    },
    onSuccess: async ({ deletedThreadId, nextThreadId }) => {
      console.log('[useThreadManagement] Delete mutation success:', {
        deletedThreadId,
        nextThreadId,
        wasCurrentThread: currentThreadId === deletedThreadId
      });

      // Remove deleted thread detail from cache để tránh 404 error
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

      // Chỉ invalidate list queries, KHÔNG invalidate detail queries
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query) => {
          // Chỉ invalidate queries có 'list' hoặc 'paginated' trong key
          return query.queryKey.includes('list') || query.queryKey.includes('paginated');
        }
      });

      // Update current thread state
      setCurrentThreadId(nextThreadId);
      setDeletingThreadId(null);

      // Dispatch thread deletion event to Redux với nextThreadId
      dispatch(threadDeleted({ threadId: deletedThreadId, nextThreadId }));

      // Nếu có next thread và thread bị xóa là current thread, auto-switch
      if (nextThreadId && currentThreadId === deletedThreadId) {
        console.log('[useThreadManagement] Auto-switching to next thread:', nextThreadId);
        try {
          // Load next thread vào chat
          await chatIntegrationService.loadThreadToChat(nextThreadId);
        } catch (error) {
          console.error('[useThreadManagement] Failed to auto-switch to next thread:', error);
        }
      }

      // Trigger callback
      onThreadDeleted?.(deletedThreadId);
    },
    onError: (error) => {
      console.error('[useThreadManagement] Failed to delete thread:', error);
      setDeletingThreadId(null);
    },
  });

  // Start thread function
  const startThread = useCallback((threadId: string) => {
    if (startingThreadId || deletingThreadId) return;
    startThreadMutation.mutate(threadId);
  }, [startThreadMutation, startingThreadId, deletingThreadId]);

  // Delete thread function
  const deleteThread = useCallback((threadId: string) => {
    if (startingThreadId || deletingThreadId) return;
    deleteThreadMutation.mutate(threadId);
  }, [deleteThreadMutation, startingThreadId, deletingThreadId]);

  // Navigate to thread detail
  const viewThreadDetail = useCallback((threadId: string) => {
    navigate(`/threads/${threadId}`);
  }, [navigate]);

  // Check if thread is active
  const isThreadActive = useCallback((threadId: string) => {
    return currentThreadId === threadId;
  }, [currentThreadId]);

  // Check if thread is in loading state
  const isThreadStarting = useCallback((threadId: string) => {
    return startingThreadId === threadId;
  }, [startingThreadId]);

  // Check if thread is being deleted
  const isThreadDeleting = useCallback((threadId: string) => {
    return deletingThreadId === threadId;
  }, [deletingThreadId]);

  return {
    // State
    currentThreadId,
    startingThreadId,
    deletingThreadId,

    // Actions
    startThread,
    deleteThread,
    viewThreadDetail,

    // Checkers
    isThreadActive,
    isThreadStarting,
    isThreadDeleting,

    // Loading states
    isStarting: startThreadMutation.isPending,
    isDeleting: deleteThreadMutation.isPending,

    // Errors
    startError: startThreadMutation.error,
    deleteError: deleteThreadMutation.error,
  };
};
