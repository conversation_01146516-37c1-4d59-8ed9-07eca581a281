# Thread Delete API Call Fix

## Vấn đề

Sau khi xóa thread, system đang gọi lại API chi tiết của thread vừa xóa, dẫn đến lỗi 404:

1. **Invalidate All Queries**: `invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL })` invalidate TẤT CẢ queries bao gồm cả detail queries
2. **Detail Query Refetch**: Thread detail query bị invalidate và cố gắng refetch API của thread đã xóa
3. **Switch to Deleted Thread**: `switchToThread` gọi `loadSpecificThread` với thread ID đã xóa

## Nguyên nhân chi tiết

### 1. Query Invalidation Scope quá rộng

```typescript
// TRƯỚC - Invalidate tất cả
queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

// Điều này invalidate:
// - ['threads', 'list', ...]     ✅ Cần thiết
// - ['threads', 'detail', id]    ❌ Gây lỗi 404
```

### 2. Race Condition trong Switch Logic

```typescript
// Thread được xóa
await ThreadsService.deleteThread(threadId);

// Cố gắng switch sang thread khác
await externalChatStream.switchToThread(nextThreadId);

// Nhưng nextThreadId có thể cũng bị xóa hoặc không tồn tại
```

### 3. Missing Error Handling

- `loadSpecificThread` không handle 404 gracefully
- `switchToThread` không có fallback khi target thread không tồn tại

## Giải pháp

### 1. Selective Query Invalidation

**Trước:**
```typescript
queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
```

**Sau:**
```typescript
// Remove deleted thread detail từ cache
queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

// Chỉ invalidate list queries
queryClient.invalidateQueries({ 
  queryKey: THREADS_QUERY_KEYS.ALL,
  predicate: (query) => {
    return query.queryKey.includes('list') || query.queryKey.includes('paginated');
  }
});
```

### 2. Enhanced Error Handling trong loadSpecificThread

**Trước:**
```typescript
const threadDetail = await ThreadsService.getThreadDetail(targetThreadId);
if (threadDetail) {
  // Load thread
} else {
  throw new Error('Thread not found');
}
```

**Sau:**
```typescript
try {
  const threadDetail = await ThreadsService.getThreadDetail(targetThreadId);
  if (threadDetail) {
    // Load thread
  } else {
    throw new Error('Thread not found');
  }
} catch (apiError) {
  if (apiError instanceof Error && apiError.message.includes('404')) {
    console.warn('[useChatStream] Thread not found (possibly deleted):', targetThreadId);
    throw new Error(`Thread ${targetThreadId} not found (possibly deleted)`);
  }
  throw apiError;
}
```

### 3. Safe Switch Logic

**Trước:**
```typescript
await loadSpecificThread(newThreadId);
config.threadEvents?.onThreadSwitched?.(previousThreadId, newThreadId);
```

**Sau:**
```typescript
try {
  await loadSpecificThread(newThreadId);
  config.threadEvents?.onThreadSwitched?.(previousThreadId, newThreadId);
} catch (loadError) {
  console.error('[useChatStream] Failed to load target thread, clearing state:', loadError);
  setThreadId(null);
  setThreadName(null);
  setError(errorMessage);
  
  // Emit switched event để parent component biết
  config.threadEvents?.onThreadSwitched?.(previousThreadId, '');
  throw loadError;
}
```

### 4. Protected Auto-Switch

**Trước:**
```typescript
if (externalChatStream && externalChatStream.switchToThread) {
  await externalChatStream.switchToThread(nextThreadId);
}
```

**Sau:**
```typescript
if (externalChatStream && externalChatStream.switchToThread && nextThreadId) {
  try {
    await externalChatStream.switchToThread(nextThreadId);
  } catch (switchError) {
    console.error('[useThreadManagementWithIntegration] Failed to switch to next thread:', switchError);
    nextThreadId = null;
  }
}
```

## Files được sửa

### 1. `useThreadManagementWithIntegration.ts`
- ✅ Selective query invalidation
- ✅ Remove deleted thread detail từ cache
- ✅ Protected auto-switch logic

### 2. `useThreadManagement.ts`
- ✅ Selective query invalidation
- ✅ Remove deleted thread detail từ cache

### 3. `ThreadsPage.tsx`
- ✅ Selective query invalidation cho tất cả thread operations
- ✅ Remove deleted thread detail trong event handlers

### 4. `thread-integration.service.ts`
- ✅ Selective query invalidation
- ✅ Remove deleted thread detail trước khi invalidate

### 5. `useChatStream.ts`
- ✅ Enhanced error handling trong `loadSpecificThread`
- ✅ Safe switch logic trong `switchToThread`
- ✅ Graceful handling của 404 errors

## Luồng hoạt động mới

### Khi xóa thread:

1. **Delete API Call**
   ```
   ThreadsService.deleteThread(threadId) ✅
   ```

2. **Cache Management**
   ```
   removeQueries({ queryKey: DETAIL(deletedThreadId) }) ✅
   invalidateQueries({ predicate: isListQuery }) ✅
   ```

3. **Auto-Switch (nếu cần)**
   ```
   try {
     switchToThread(nextThreadId) ✅
   } catch {
     handle gracefully ✅
   }
   ```

4. **State Update**
   ```
   setCurrentThreadId(nextThreadId) ✅
   onThreadDeleted(deletedThreadId, nextThreadId) ✅
   ```

## Kết quả

- ✅ Không còn API calls với thread ID đã xóa
- ✅ Không còn 404 errors từ detail queries
- ✅ Graceful handling khi switch thread thất bại
- ✅ Cache được quản lý đúng cách
- ✅ UI sync đúng với backend state

## Testing

Để test fix này:

1. Tạo một thread và load detail
2. Xóa thread đó
3. Verify không có API call 404 trong Network tab
4. Verify UI update đúng cách
5. Test với multiple threads
6. Test với thread cuối cùng (auto-create)
